const axios = require('axios');
const fs = require('fs').promises;

async function debugComfyUI() {
    console.log('🔍 开始调试ComfyUI工作流...\n');
    
    try {
        // 1. 读取工作流文件
        console.log('1️⃣ 读取工作流文件...');
        const workflowData = await fs.readFile('./reba.json', 'utf8');
        const workflow = JSON.parse(workflowData);
        console.log('✅ 工作流文件读取成功');
        
        // 2. 修改工作流参数
        console.log('\n2️⃣ 修改工作流参数...');
        
        // 更新正面提示词
        const positivePrompt = 'best quality, masterpiece, a beautiful mountain landscape at sunset, peaceful scene, high quality';
        if (workflow['27'] && workflow['27'].inputs) {
            workflow['27'].inputs.text = positivePrompt;
            console.log('✅ 正面提示词已更新:', positivePrompt);
        }
        
        // 更新负面提示词
        const negativePrompt = 'blurry, low quality, distorted, ugly, worst quality, bad anatomy';
        if (workflow['1'] && workflow['1'].inputs) {
            workflow['1'].inputs.text = negativePrompt;
            console.log('✅ 负面提示词(节点1)已更新');
        }
        if (workflow['20'] && workflow['20'].inputs) {
            workflow['20'].inputs.text = negativePrompt;
            console.log('✅ 负面提示词(节点20)已更新');
        }
        
        // 更新图片尺寸
        const width = 720, height = 480;
        if (workflow['25'] && workflow['25'].inputs) {
            workflow['25'].inputs.width = width;
            workflow['25'].inputs.height = height;
            workflow['25'].inputs.batch_size = 1;
            console.log(`✅ 图片尺寸已更新: ${width}x${height}`);
        }
        
        // 生成随机种子
        const seed = Math.floor(Math.random() * 1000000000);
        if (workflow['26'] && workflow['26'].inputs) {
            workflow['26'].inputs.seed = seed;
            console.log('✅ 随机种子已生成:', seed);
        }
        
        // 3. 发送到ComfyUI
        console.log('\n3️⃣ 发送请求到ComfyUI...');
        console.log('URL:', 'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/prompt');
        
        const startTime = Date.now();
        
        const response = await axios.post(
            'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/prompt',
            {
                prompt: workflow
            },
            {
                timeout: 30000,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            }
        );
        
        const endTime = Date.now();
        console.log(`✅ 请求成功! 耗时: ${endTime - startTime}ms`);
        console.log('响应状态:', response.status);
        console.log('响应数据:', JSON.stringify(response.data, null, 2));
        
        const promptId = response.data.prompt_id;
        console.log(`📋 获得prompt_id: ${promptId}`);
        
        // 4. 监控处理进度
        console.log('\n4️⃣ 监控处理进度...');
        await monitorProgress(promptId);
        
    } catch (error) {
        console.error('\n❌ 调试失败:');
        console.error('错误信息:', error.message);
        
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应头:', error.response.headers);
            console.error('响应数据:', error.response.data);
        }
        
        if (error.code) {
            console.error('错误代码:', error.code);
        }
    }
}

async function monitorProgress(promptId) {
    const maxWaitTime = 180000; // 3分钟
    const checkInterval = 5000; // 5秒检查一次
    const startTime = Date.now();
    
    console.log(`开始监控进度: ${promptId}`);
    
    while (Date.now() - startTime < maxWaitTime) {
        try {
            // 检查队列状态
            console.log('📊 检查队列状态...');
            const queueResponse = await axios.get(
                'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/queue',
                { timeout: 10000 }
            );
            
            const queueData = queueResponse.data;
            console.log('队列数据:', JSON.stringify(queueData, null, 2));
            
            const isInQueue = queueData.queue_running?.some(item => item[1] === promptId) ||
                             queueData.queue_pending?.some(item => item[1] === promptId);
            
            if (!isInQueue) {
                console.log('✅ 任务不在队列中，检查历史记录...');
                
                // 检查历史记录
                const historyResponse = await axios.get(
                    `https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/history/${promptId}`,
                    { timeout: 10000 }
                );
                
                const historyData = historyResponse.data;
                console.log('历史数据:', JSON.stringify(historyData, null, 2));
                
                if (historyData[promptId]) {
                    const outputs = historyData[promptId].outputs;
                    console.log('找到输出:', outputs);
                    
                    // 查找图片
                    for (const nodeId in outputs) {
                        const nodeOutput = outputs[nodeId];
                        if (nodeOutput.images && nodeOutput.images.length > 0) {
                            const imageInfo = nodeOutput.images[0];
                            console.log('🖼️ 找到图片:', imageInfo);
                            
                            const imageUrl = `https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/view?filename=${imageInfo.filename}&subfolder=${imageInfo.subfolder || ''}&type=${imageInfo.type || 'output'}`;
                            console.log('🎉 图片URL:', imageUrl);
                            
                            // 测试图片URL
                            try {
                                const imageResponse = await axios.head(imageUrl, { timeout: 10000 });
                                console.log('✅ 图片URL可访问，状态码:', imageResponse.status);
                            } catch (imageError) {
                                console.log('⚠️ 图片URL访问失败:', imageError.message);
                            }
                            
                            return {
                                imageUrl: imageUrl,
                                filename: imageInfo.filename
                            };
                        }
                    }
                }
            } else {
                console.log('⏳ 任务还在队列中，继续等待...');
            }
            
            await new Promise(resolve => setTimeout(resolve, checkInterval));
            
        } catch (error) {
            console.error('监控失败:', error.message);
            if (error.response?.status === 404) {
                console.log('任务还在处理中...');
            }
            await new Promise(resolve => setTimeout(resolve, checkInterval));
        }
    }
    
    console.log('⏰ 监控超时');
}

// 运行调试
debugComfyUI();
