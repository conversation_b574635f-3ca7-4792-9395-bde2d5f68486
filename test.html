<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ComfyUI API测试</h1>
        
        <div class="form-group">
            <label for="prompt">图片描述：</label>
            <textarea id="prompt" placeholder="请输入图片描述，至少10个字符...">a beautiful landscape with mountains and sunset, high quality, detailed</textarea>
        </div>
        
        <div class="form-group">
            <label for="negativePrompt">反向提示词（可选）：</label>
            <textarea id="negativePrompt" placeholder="输入不想要的内容..."></textarea>
        </div>

        <div class="form-group">
            <label for="size">图片尺寸：</label>
            <select id="size">
                <option value="480x720">纵向 (480x720)</option>
                <option value="720x480">横向 (720x480)</option>
                <option value="720x720">正方形 (720x720)</option>
                <option value="720x1080">高清纵向 (720x1080)</option>
            </select>
        </div>
        
        <button onclick="testGenerate()" id="generateBtn">生成图片</button>
        <button onclick="testHealth()">健康检查</button>
        <button onclick="testQueue()">队列信息</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        let currentTaskId = null;
        
        function showResult(message, type = 'success') {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }
        
        async function testHealth() {
            try {
                showResult('正在检查服务器健康状态...', 'loading');
                
                const response = await fetch('/health');
                const data = await response.json();
                
                showResult(`健康检查成功:\n${JSON.stringify(data, null, 2)}`, 'success');
            } catch (error) {
                showResult(`健康检查失败:\n${error.message}`, 'error');
            }
        }
        
        async function testQueue() {
            try {
                showResult('正在获取队列信息...', 'loading');
                
                const response = await fetch('/api/queue-info');
                const data = await response.json();
                
                showResult(`队列信息:\n${JSON.stringify(data, null, 2)}`, 'success');
            } catch (error) {
                showResult(`获取队列信息失败:\n${error.message}`, 'error');
            }
        }
        
        async function testGenerate() {
            try {
                const prompt = document.getElementById('prompt').value.trim();
                const negativePrompt = document.getElementById('negativePrompt').value.trim();
                const size = document.getElementById('size').value;
                
                if (!prompt || prompt.length < 10) {
                    showResult('描述至少需要10个字符', 'error');
                    return;
                }
                
                const generateBtn = document.getElementById('generateBtn');
                generateBtn.disabled = true;
                generateBtn.textContent = '生成中...';
                
                showResult('正在发送生成请求...', 'loading');
                
                const response = await fetch('/api/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        negativePrompt: negativePrompt,
                        size: size
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    currentTaskId = data.taskId;
                    showResult(`生成请求成功:\n${JSON.stringify(data, null, 2)}`, 'success');
                    
                    // 开始监控状态
                    if (currentTaskId) {
                        setTimeout(() => checkStatus(), 2000);
                    }
                } else {
                    showResult(`生成请求失败:\n${JSON.stringify(data, null, 2)}`, 'error');
                }
                
            } catch (error) {
                showResult(`生成请求失败:\n${error.message}`, 'error');
            } finally {
                const generateBtn = document.getElementById('generateBtn');
                generateBtn.disabled = false;
                generateBtn.textContent = '生成图片';
            }
        }
        
        async function checkStatus() {
            if (!currentTaskId) return;
            
            try {
                const response = await fetch(`/api/status/${currentTaskId}`);
                const data = await response.json();
                
                showResult(`任务状态:\n${JSON.stringify(data, null, 2)}`, 'success');
                
                if (data.status === 'completed') {
                    currentTaskId = null;
                } else if (data.status === 'processing' || data.status === 'queued') {
                    // 继续监控
                    setTimeout(() => checkStatus(), 3000);
                }
                
            } catch (error) {
                showResult(`状态查询失败:\n${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动检查健康状态
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
