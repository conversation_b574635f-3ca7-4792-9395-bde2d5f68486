<template>
  <div class="app-container">
    <el-card class="main-card" :style="{ maxWidth: '800px', width: '100%' }">
      <!-- 主页面 -->
      <div v-if="currentPage === 'main'" class="main-page">
        <div class="header">
          <div class="title-row">
            <h1 class="title">AI图片生成器</h1>
            <div class="config-buttons">
              <el-button
                type="info"
                size="small"
                @click="showApiConfig = true"
                :icon="Setting"
                circle
                title="前端API配置"
              />
              <el-button
                type="warning"
                size="small"
                @click="showComfyUIConfig = true"
                :icon="Monitor"
                circle
                title="ComfyUI服务器配置"
              />
              <el-button
                type="danger"
                size="small"
                @click="showNetworkDiagnostic = true"
                :icon="Tools"
                circle
                title="网络诊断"
              />
            </div>
          </div>
          <p class="subtitle">输入您的创意描述，AI将为您生成精美图片</p>

          <!-- API连接状态 -->
          <div class="api-status">
            <div class="status-item">
              <el-tag
                :type="apiConnected ? 'success' : 'danger'"
                size="small"
                effect="light"
              >
                <el-icon><Connection /></el-icon>
                前端API: {{ apiConnected ? '已连接' : '未连接' }}
              </el-tag>
              <span class="api-url">{{ currentApiUrl }}</span>
            </div>
            <div class="status-item">
              <el-tag
                :type="comfyUIConnected ? 'success' : 'danger'"
                size="small"
                effect="light"
              >
                <el-icon><Monitor /></el-icon>
                ComfyUI: {{ comfyUIConnected ? '已连接' : '未连接' }}
              </el-tag>
              <span class="api-url">{{ currentComfyUIUrl }}</span>
            </div>
          </div>
        </div>

        <el-form :model="form" label-position="top" class="generate-form">
          <!-- 正面提示词 -->
          <el-form-item label="图片描述">
            <el-input
              v-model="form.prompt"
              type="textarea"
              :rows="4"
              placeholder="请输入您想要生成的图片描述，例如：一只可爱的小猫在花园里玩耍，阳光明媚，高清摄影风格..."
              maxlength="1000"
              show-word-limit
              @input="checkFormValidity"
            />
          </el-form-item>

          <!-- 反向提示词 -->
          <el-form-item>
            <template #label>
              <div class="negative-label">
                <el-icon><Close /></el-icon>
                <span>反向提示词（可选）</span>
                <span class="label-hint">描述不想要的内容</span>
              </div>
            </template>
            <el-input
              v-model="form.negativePrompt"
              type="textarea"
              :rows="3"
              placeholder="例如：低质量、模糊、变形、多余的手指、错误的解剖结构..."
              maxlength="500"
              show-word-limit
              class="negative-textarea"
            />
          </el-form-item>

          <!-- 工作流选择 -->
          <el-form-item label="生成风格">
            <el-select v-model="form.workflowId" placeholder="选择生成风格" @change="handleWorkflowChange">
              <el-option
                v-for="workflow in availableWorkflows"
                :key="workflow.id"
                :label="workflow.name"
                :value="workflow.id"
              />
            </el-select>
          </el-form-item>

          <!-- 图片尺寸 -->
          <el-form-item label="图片尺寸">
            <el-select v-model="form.size" placeholder="选择图片尺寸">
              <el-option label="纵向 (480x720)" value="480x720" />
              <el-option label="横向 (720x480)" value="720x480" />
              <el-option label="正方形 (720x720)" value="720x720" />
              <el-option label="高清纵向 (720x1080)" value="720x1080" />
            </el-select>
          </el-form-item>

          <!-- 生成按钮 -->
          <el-form-item>
            <el-button
              type="primary"
              size="large"
              :disabled="!isFormValid || isGenerating"
              :loading="isGenerating"
              @click="handleGenerate"
              style="width: 100%"
            >
              <template v-if="!isGenerating">
                <el-icon><Picture /></el-icon>
                <span>生成图片</span>
              </template>
              <template v-else>
                <span>生成中...</span>
              </template>
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 队列信息 -->
        <div class="queue-info">
          <el-card shadow="never" style="background: rgba(255,255,255,0.95)">
            <div class="queue-status">
              <span>当前队列：</span>
              <el-tag type="info" size="large">{{ queueInfo.queueLength }} 个任务等待中</el-tag>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 等待页面 -->
      <div v-else-if="currentPage === 'waiting'" class="waiting-page">
        <transition name="slide-up" appear>
          <div class="waiting-container">
            <div class="loading-animation">
              <el-icon class="loading-icon" size="60"><Loading /></el-icon>
            </div>
            
            <h2 class="waiting-title">正在生成您的图片...</h2>
            <p class="waiting-subtitle">AI正在努力创作中，请稍候</p>
            
            <div class="progress-section">
              <el-progress
                :percentage="taskProgress"
                :stroke-width="8"
                status="success"
                :show-text="false"
              />
              <div class="progress-info">
                <span>{{ taskStatusText }}</span>
                <span v-if="taskPosition > 0">排队位置: {{ taskPosition }}</span>
              </div>
            </div>

            <div class="estimated-time">
              <el-icon><Clock /></el-icon>
              <span>预计等待时间：{{ estimatedTime }}</span>
            </div>

            <el-button type="danger" @click="handleCancel">
              <el-icon><Close /></el-icon>
              取消生成
            </el-button>
          </div>
        </transition>
      </div>

      <!-- 结果页面 -->
      <div v-else-if="currentPage === 'result'" class="result-page">
        <transition name="fade" appear>
          <div class="result-container">
            <h2 class="result-title">生成完成！</h2>
            
            <div class="image-container">
              <el-image
                :src="generatedImage.imageUrl"
                :alt="generatedImage.prompt"
                fit="contain"
                style="max-width: 100%; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2)"
                :preview-src-list="[generatedImage.imageUrl]"
              />
              
              <div class="image-actions">
                <el-button type="success" @click="handleDownload">
                  <el-icon><Download /></el-icon>
                  下载图片
                </el-button>
                <el-button type="info" @click="handleShare">
                  <el-icon><Share /></el-icon>
                  分享
                </el-button>
              </div>
            </div>

            <el-card class="prompt-info" shadow="never">
              <h3>生成信息</h3>
              <p><strong>描述：</strong>{{ generatedImage.prompt }}</p>
              <p v-if="generatedImage.negativePrompt"><strong>反向提示词：</strong>{{ generatedImage.negativePrompt }}</p>
              <p><strong>尺寸：</strong>{{ generatedImage.size }}</p>
              <p><strong>文件名：</strong>{{ generatedImage.filename }}</p>
              <p><strong>生成时间：</strong>{{ formatTime(generatedImage.generatedAt) }}</p>
            </el-card>

            <div class="result-actions">
              <el-button type="primary" @click="handleGenerateAgain">
                <el-icon><Refresh /></el-icon>
                再次生成
              </el-button>
              <el-button @click="handleNewPrompt">
                <el-icon><Edit /></el-icon>
                新的创作
              </el-button>
            </div>
          </div>
        </transition>
      </div>

      <!-- 错误页面 -->
      <div v-else-if="currentPage === 'error'" class="error-page">
        <div class="error-container">
          <el-icon class="error-icon" size="80" color="#f56c6c"><CircleClose /></el-icon>
          <h2 class="error-title">生成失败</h2>
          <p class="error-message">{{ errorMessage }}</p>
          
          <div class="error-actions">
            <el-button type="primary" @click="handleRetry">
              <el-icon><Refresh /></el-icon>
              重试
            </el-button>
            <el-button @click="handleBackHome">
              <el-icon><House /></el-icon>
              返回首页
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- API配置对话框 -->
    <ApiConfig
      v-model="showApiConfig"
      @config-changed="handleApiConfigChanged"
    />

    <!-- API自动检测对话框 -->
    <ApiDetection
      v-model="showApiDetection"
      @detection-complete="handleDetectionComplete"
      @manual-config="handleManualConfig"
    />

    <!-- ComfyUI配置对话框 -->
    <ComfyUIConfig
      v-model="showComfyUIConfig"
      @config-changed="handleComfyUIConfigChanged"
    />

    <!-- 网络诊断对话框 -->
    <NetworkDiagnostic
      v-model="showNetworkDiagnostic"
    />
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Picture,
  Loading,
  Clock,
  Close,
  Download,
  Share,
  Refresh,
  Edit,
  CircleClose,
  House,
  Setting,
  Connection,
  Monitor,
  Tools
} from '@element-plus/icons-vue'
import { generateImage, getTaskStatus, cancelTask, getQueueInfo, getWorkflows } from './api/imageApi.js'
import { getApiConfig, testApiConnection } from './utils/apiConfig.js'
import { checkComfyUIStatus } from './api/comfyuiApi.js'
import ApiConfig from './components/ApiConfig.vue'
import ApiDetection from './components/ApiDetection.vue'
import ComfyUIConfig from './components/ComfyUIConfig.vue'
import NetworkDiagnostic from './components/NetworkDiagnostic.vue'

export default {
  name: 'App',
  components: {
    Picture,
    Loading,
    Clock,
    Close,
    Download,
    Share,
    Refresh,
    Edit,
    CircleClose,
    House,
    Setting,
    Connection,
    Monitor,
    Tools,
    ApiConfig,
    ApiDetection,
    ComfyUIConfig,
    NetworkDiagnostic
  },
  setup() {
    // 响应式数据
    const currentPage = ref('main')
    const isGenerating = ref(false)
    const isFormValid = ref(false)
    
    const form = reactive({
      prompt: '',
      negativePrompt: '',
      size: '720x480',
      workflowId: 'reba'
    })

    const availableWorkflows = ref([])
    const showApiConfig = ref(false)
    const showApiDetection = ref(false)
    const showComfyUIConfig = ref(false)
    const showNetworkDiagnostic = ref(false)
    const apiConnected = ref(false)
    const currentApiUrl = ref('')
    const comfyUIConnected = ref(false)
    const currentComfyUIUrl = ref('')

    const queueInfo = reactive({
      queueLength: 0,
      processingCount: 0
    })
    
    // 任务相关
    const currentTaskId = ref(null)
    const taskProgress = ref(0)
    const taskStatusText = ref('准备中')
    const taskPosition = ref(0)
    const estimatedTime = ref('计算中...')
    
    // 结果相关
    const generatedImage = reactive({
      imageUrl: '',
      prompt: '',
      negativePrompt: '',
      size: '',
      filename: '',
      generatedAt: null
    })
    
    const errorMessage = ref('')
    
    // 定时器
    let statusCheckInterval = null
    let queueUpdateInterval = null
    
    // 方法
    const checkFormValidity = () => {
      isFormValid.value = form.prompt.trim().length >= 10
    }
    
    const updateQueueInfo = async () => {
      try {
        const info = await getQueueInfo()
        Object.assign(queueInfo, info)
      } catch (error) {
        console.error('获取队列信息失败:', error)
      }
    }

    const loadWorkflows = async () => {
      try {
        const response = await getWorkflows()
        availableWorkflows.value = response.workflows || []
        console.log('工作流列表加载成功:', availableWorkflows.value)
      } catch (error) {
        console.error('加载工作流列表失败:', error)
        ElMessage.error('加载工作流列表失败')
      }
    }

    const handleWorkflowChange = (workflowId) => {
      console.log('切换工作流:', workflowId)
      // 可以在这里根据不同工作流调整默认参数
      switch (workflowId) {
        case 'anime':
          form.size = '512x768'
          break
        case 'landscape':
          form.size = '1024x768'
          break
        default:
          form.size = '720x480'
      }
    }
    
    const handleGenerate = async () => {
      if (!isFormValid.value) {
        ElMessage.warning('请输入至少10个字符的图片描述')
        return
      }
      
      try {
        isGenerating.value = true
        currentPage.value = 'waiting'
        
        const response = await generateImage({
          prompt: form.prompt.trim(),
          negativePrompt: form.negativePrompt.trim(),
          size: form.size,
          workflowId: form.workflowId
        })
        
        currentTaskId.value = response.taskId
        taskProgress.value = 5
        taskStatusText.value = '已加入队列'
        
        // 开始监控任务状态
        startTaskMonitoring()
        
      } catch (error) {
        console.error('生成请求失败:', error)

        // 增强错误处理和用户引导
        let userMessage = '请求发送失败，请检查网络连接'
        let showDiagnostic = false

        if (error.message.includes('网络连接失败') ||
            error.message.includes('请检查网络连接') ||
            error.message.includes('连接被拒绝') ||
            error.message.includes('Network Error')) {
          userMessage = '网络连接失败，请检查网络连接状态'
          showDiagnostic = true
        } else if (error.message.includes('404') || error.message.includes('API接口不存在')) {
          userMessage = 'API接口不存在，请检查API地址配置'
        } else if (error.message.includes('ComfyUI')) {
          userMessage = 'ComfyUI服务器连接失败，请检查ComfyUI配置'
        } else if (error.message) {
          userMessage = error.message
        }

        showError(userMessage)

        // 如果是网络问题，提示用户使用诊断工具
        if (showDiagnostic) {
          setTimeout(() => {
            ElMessage({
              message: '检测到网络连接问题，建议点击右上角红色工具图标进行网络诊断',
              type: 'warning',
              duration: 8000,
              showClose: true
            })
          }, 1000)
        }
      }
    }
    
    const startTaskMonitoring = () => {
      statusCheckInterval = setInterval(async () => {
        try {
          const status = await getTaskStatus(currentTaskId.value)
          
          taskProgress.value = status.progress || 0
          taskPosition.value = status.position || 0
          estimatedTime.value = status.estimatedTime || '计算中...'
          
          switch (status.status) {
            case 'queued':
              taskStatusText.value = '排队等待中'
              break
            case 'processing':
              taskStatusText.value = '正在生成'
              break
            case 'completed':
              clearInterval(statusCheckInterval)
              showResult(status.result)
              break
            case 'failed':
              clearInterval(statusCheckInterval)
              showError(status.error || '生成失败')
              break
          }
          
        } catch (error) {
          console.error('状态检查失败:', error)
        }
      }, 2000)
    }
    
    const showResult = (result) => {
      Object.assign(generatedImage, result)
      currentPage.value = 'result'
      isGenerating.value = false
      ElMessage.success('图片生成完成！')
    }
    
    const showError = (message) => {
      errorMessage.value = message
      currentPage.value = 'error'
      isGenerating.value = false
    }
    
    const handleCancel = async () => {
      if (currentTaskId.value) {
        try {
          await cancelTask(currentTaskId.value)
          clearInterval(statusCheckInterval)
          currentPage.value = 'main'
          isGenerating.value = false
          ElMessage.info('已取消生成')
        } catch (error) {
          console.error('取消失败:', error)
        }
      }
    }
    
    const handleDownload = () => {
      const link = document.createElement('a')
      link.href = generatedImage.imageUrl
      link.download = generatedImage.filename || `ai-generated-${Date.now()}.png`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      ElMessage.success('图片下载中...')
    }
    
    const handleShare = async () => {
      if (navigator.share) {
        try {
          await navigator.share({
            title: 'AI生成的图片',
            text: '看看我用AI生成的图片！',
            url: window.location.href
          })
        } catch (error) {
          console.log('分享取消')
        }
      } else {
        try {
          await navigator.clipboard.writeText(window.location.href)
          ElMessage.success('链接已复制到剪贴板')
        } catch (error) {
          ElMessage.error('分享失败')
        }
      }
    }
    
    const handleGenerateAgain = () => {
      currentPage.value = 'main'
    }
    
    const handleNewPrompt = () => {
      form.prompt = ''
      form.negativePrompt = ''
      checkFormValidity()
      currentPage.value = 'main'
    }
    
    const handleRetry = () => {
      handleGenerate()
    }
    
    const handleBackHome = () => {
      currentPage.value = 'main'
    }
    
    const formatTime = (dateString) => {
      if (!dateString) return ''
      return new Date(dateString).toLocaleString('zh-CN')
    }

    // API配置相关方法
    const checkApiConnection = async () => {
      try {
        const connected = await testApiConnection()
        apiConnected.value = connected

        if (!connected) {
          ElMessage.warning('API连接失败，请检查配置')
        }
      } catch (error) {
        apiConnected.value = false
        console.error('API连接检查失败:', error)
      }
    }

    const updateApiUrl = () => {
      const config = getApiConfig()
      currentApiUrl.value = config.baseURL || '相对路径'
    }

    const handleApiConfigChanged = () => {
      updateApiUrl()
      checkApiConnection()
      // 重新加载工作流列表
      loadWorkflows()
      ElMessage.success('API配置已更新')
    }

    const handleDetectionComplete = (detectedUrl) => {
      updateApiUrl()
      checkApiConnection()
      loadWorkflows()
      ElMessage.success(`已自动配置API地址: ${detectedUrl}`)
    }

    const handleManualConfig = () => {
      showApiConfig.value = true
    }

    // ComfyUI配置相关方法
    const checkComfyUIConnection = async () => {
      try {
        const status = await checkComfyUIStatus()
        comfyUIConnected.value = status.connected
        currentComfyUIUrl.value = status.baseURL || '未配置'

        if (!status.connected) {
          console.warn('ComfyUI连接失败:', status.message)
        }
      } catch (error) {
        comfyUIConnected.value = false
        currentComfyUIUrl.value = '检查失败'
        console.error('ComfyUI连接检查失败:', error)
      }
    }

    const handleComfyUIConfigChanged = () => {
      checkComfyUIConnection()
      ElMessage.success('ComfyUI配置已更新')
    }

    // 初始化时检查是否需要自动检测
    const checkInitialApiConfig = () => {
      const config = getApiConfig()
      if (!config.baseURL) {
        // 如果没有配置API地址，显示自动检测对话框
        setTimeout(() => {
          showApiDetection.value = true
        }, 1000)
      }
    }
    
    // 生命周期
    onMounted(() => {
      updateApiUrl()
      checkApiConnection()
      checkComfyUIConnection()
      checkInitialApiConfig()
      loadWorkflows()
      updateQueueInfo()
      queueUpdateInterval = setInterval(updateQueueInfo, 5000)
    })
    
    onUnmounted(() => {
      if (statusCheckInterval) {
        clearInterval(statusCheckInterval)
      }
      if (queueUpdateInterval) {
        clearInterval(queueUpdateInterval)
      }
    })
    
    return {
      currentPage,
      isGenerating,
      isFormValid,
      form,
      availableWorkflows,
      showApiConfig,
      showApiDetection,
      showComfyUIConfig,
      showNetworkDiagnostic,
      apiConnected,
      currentApiUrl,
      comfyUIConnected,
      currentComfyUIUrl,
      queueInfo,
      taskProgress,
      taskStatusText,
      taskPosition,
      estimatedTime,
      generatedImage,
      errorMessage,
      checkFormValidity,
      handleGenerate,
      handleCancel,
      handleDownload,
      handleShare,
      handleGenerateAgain,
      handleNewPrompt,
      handleRetry,
      handleBackHome,
      handleWorkflowChange,
      handleApiConfigChanged,
      handleDetectionComplete,
      handleManualConfig,
      handleComfyUIConfigChanged,
      formatTime
    }
  }
}
</script>

<style scoped>
.app-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.main-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

/* 头部样式 */
.header {
  text-align: center;
  margin-bottom: 40px;
}

.title-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 10px;
}

.config-buttons {
  display: flex;
  gap: 8px;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin: 0;
}

.subtitle {
  font-size: 1.1rem;
  color: #666;
  font-weight: 300;
  margin-bottom: 15px;
}

.api-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.api-url {
  font-family: 'Courier New', monospace;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
}

/* 表单样式 */
.generate-form {
  margin-bottom: 30px;
}

.negative-label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #e67e22;
  font-weight: 500;
}

.label-hint {
  font-size: 12px;
  color: #95a5a6;
  font-weight: 400;
  margin-left: auto;
}

/* 队列信息样式 */
.queue-info {
  margin-top: 20px;
}

.queue-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-size: 16px;
}

/* 等待页面样式 */
.waiting-container {
  text-align: center;
  padding: 40px 20px;
}

.loading-animation {
  margin-bottom: 30px;
}

.loading-icon {
  animation: spin 1s linear infinite;
  color: #667eea;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.waiting-title {
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 10px;
  font-weight: 600;
}

.waiting-subtitle {
  color: #666;
  margin-bottom: 30px;
  font-size: 1.1rem;
}

.progress-section {
  margin-bottom: 25px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
  font-size: 14px;
  color: #666;
}

.estimated-time {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: #f8f9fa;
  padding: 15px;
  border-radius: 10px;
  margin-bottom: 25px;
  font-size: 14px;
  color: #555;
}

/* 结果页面样式 */
.result-container {
  text-align: center;
  padding: 20px;
}

.result-title {
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 30px;
  font-weight: 600;
}

.image-container {
  margin-bottom: 30px;
}

.image-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 20px;
  flex-wrap: wrap;
}

.prompt-info {
  text-align: left;
  margin-bottom: 30px;
  background: #f8f9fa;
}

.prompt-info h3 {
  font-size: 1.1rem;
  color: #333;
  margin-bottom: 15px;
  font-weight: 600;
}

.prompt-info p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 8px;
  font-size: 14px;
}

.result-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 错误页面样式 */
.error-container {
  text-align: center;
  padding: 40px 20px;
}

.error-icon {
  margin-bottom: 20px;
}

.error-title {
  font-size: 1.8rem;
  color: #f56c6c;
  margin-bottom: 15px;
  font-weight: 600;
}

.error-message {
  color: #666;
  margin-bottom: 30px;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .title {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .image-actions,
  .result-actions,
  .error-actions {
    flex-direction: column;
  }

  .progress-info {
    flex-direction: column;
    gap: 5px;
    text-align: center;
  }
}
</style>
