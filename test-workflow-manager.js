const axios = require('axios');

async function testWorkflowManager() {
    console.log('🔧 测试工作流管理功能...\n');
    
    const baseURL = 'http://localhost:4000';
    
    try {
        // 1. 测试后端健康检查
        console.log('1️⃣ 测试后端健康检查...');
        const healthResponse = await axios.get(`${baseURL}/api/health`);
        console.log('✅ 后端健康检查成功:', healthResponse.data);
        
        // 2. 测试获取工作流列表
        console.log('\n2️⃣ 测试获取工作流列表...');
        const workflowsResponse = await axios.get(`${baseURL}/api/workflows`);
        console.log('✅ 工作流列表获取成功:');
        workflowsResponse.data.workflows.forEach(workflow => {
            console.log(`   - ${workflow.name} (${workflow.id})`);
        });
        
        // 3. 测试获取具体工作流信息
        console.log('\n3️⃣ 测试获取工作流信息...');
        const workflows = workflowsResponse.data.workflows;
        
        for (const workflow of workflows) {
            try {
                const infoResponse = await axios.get(`${baseURL}/api/workflows/${workflow.id}`);
                console.log(`✅ ${workflow.name} 信息:`, {
                    节点数量: infoResponse.data.workflow.nodeCount,
                    包含文本编码: infoResponse.data.workflow.hasTextEncode ? '是' : '否',
                    包含图片生成: infoResponse.data.workflow.hasImageGeneration ? '是' : '否',
                    包含保存图片: infoResponse.data.workflow.hasSaveImage ? '是' : '否'
                });
            } catch (error) {
                console.log(`❌ ${workflow.name} 信息获取失败:`, error.response?.data?.message || error.message);
            }
        }
        
        // 4. 测试使用不同工作流生成图片
        console.log('\n4️⃣ 测试使用不同工作流生成图片...');
        
        const testCases = [
            {
                workflowId: 'reba',
                prompt: 'beautiful portrait of a young woman, high quality',
                negativePrompt: 'blurry, low quality',
                size: '720x480'
            },
            {
                workflowId: 'landscape',
                prompt: 'beautiful mountain landscape at sunset',
                negativePrompt: 'blurry, low quality, people',
                size: '1024x768'
            },
            {
                workflowId: 'anime',
                prompt: 'cute anime girl with colorful hair',
                negativePrompt: 'realistic, 3d, blurry',
                size: '512x768'
            }
        ];
        
        for (const testCase of testCases) {
            try {
                console.log(`\n📝 测试工作流: ${testCase.workflowId}`);
                const generateResponse = await axios.post(`${baseURL}/api/generate`, testCase);
                
                if (generateResponse.data.success) {
                    console.log(`✅ ${testCase.workflowId} 生成请求成功:`, {
                        任务ID: generateResponse.data.taskId,
                        状态: generateResponse.data.status,
                        预计时间: generateResponse.data.estimatedTime
                    });
                    
                    // 检查任务状态
                    const taskId = generateResponse.data.taskId;
                    let attempts = 0;
                    const maxAttempts = 5;
                    
                    while (attempts < maxAttempts) {
                        attempts++;
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        
                        const statusResponse = await axios.get(`${baseURL}/api/status/${taskId}`);
                        const status = statusResponse.data;
                        
                        console.log(`   📊 状态检查 ${attempts}: ${status.status} (${status.progress}%)`);
                        
                        if (status.status === 'completed' || status.status === 'failed') {
                            break;
                        }
                    }
                } else {
                    console.log(`❌ ${testCase.workflowId} 生成请求失败:`, generateResponse.data);
                }
                
            } catch (error) {
                console.log(`❌ ${testCase.workflowId} 测试失败:`, error.response?.data?.message || error.message);
            }
        }
        
        // 5. 测试错误处理
        console.log('\n5️⃣ 测试错误处理...');
        
        try {
            await axios.get(`${baseURL}/api/workflows/nonexistent`);
        } catch (error) {
            if (error.response?.status === 404) {
                console.log('✅ 不存在的工作流正确返回404错误');
            } else {
                console.log('❌ 错误处理异常:', error.message);
            }
        }
        
        try {
            await axios.post(`${baseURL}/api/generate`, {
                prompt: 'test',
                workflowId: 'nonexistent'
            });
        } catch (error) {
            if (error.response?.status === 500) {
                console.log('✅ 不存在的工作流生成请求正确返回错误');
            } else {
                console.log('❌ 工作流错误处理异常:', error.message);
            }
        }
        
        console.log('\n🏁 工作流管理功能测试完成!');
        console.log('\n📋 测试总结:');
        console.log('✅ 工作流列表获取正常');
        console.log('✅ 工作流信息查询正常');
        console.log('✅ 多工作流生成支持正常');
        console.log('✅ 错误处理机制正常');
        console.log('✅ workflow目录管理正常');
        
    } catch (error) {
        console.error('\n❌ 测试失败:');
        console.error('错误信息:', error.message);
        
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
        
        if (error.code === 'ECONNREFUSED') {
            console.error('\n💡 建议:');
            console.error('- 检查后端服务器是否启动 (npm run dev:backend)');
            console.error('- 检查端口3008是否可用');
        }
    }
}

// 运行测试
testWorkflowManager();
