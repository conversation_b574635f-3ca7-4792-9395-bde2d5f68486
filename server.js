const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs').promises;
const ImageGenerationQueue = require('./image-queue');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// 创建图片生成队列
const imageQueue = new ImageGenerationQueue({
    maxConcurrent: 2,
    maxQueueSize: 50,
    comfyUIUrl: process.env.COMFYUI_URL || 'http://localhost:8188',
    outputDir: './generated'
});

// 队列事件监听
imageQueue.on('taskCompleted', (task) => {
    console.log(`任务完成: ${task.id}`);
});

imageQueue.on('taskFailed', (task) => {
    console.log(`任务失败: ${task.id}, 错误: ${task.error}`);
});

imageQueue.on('taskStarted', (task) => {
    console.log(`开始处理任务: ${task.id}`);
});

// 生成图片的API端点
app.post('/api/generate', async (req, res) => {
    try {
        const { prompt, style, size } = req.body;

        if (!prompt || prompt.trim().length < 10) {
            return res.status(400).json({
                error: '描述至少需要10个字符'
            });
        }

        // 添加任务到队列
        const task = imageQueue.addTask({
            prompt: prompt.trim(),
            style: style || 'realistic',
            size: size || '512x512'
        });

        res.json({
            taskId: task.id,
            status: task.status,
            position: task.position,
            estimatedTime: formatTime(imageQueue.calculateEstimatedWaitTime())
        });

    } catch (error) {
        console.error('生成请求处理失败:', error);

        if (error.message === '队列已满，请稍后再试') {
            return res.status(429).json({ error: error.message });
        }

        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 获取任务状态
app.get('/api/status/:taskId', (req, res) => {
    const { taskId } = req.params;
    const task = imageQueue.getTaskStatus(taskId);

    if (!task) {
        return res.status(404).json({ error: '任务不存在' });
    }

    res.json({
        status: task.status,
        position: task.position || 0,
        estimatedTime: formatTime(imageQueue.calculateEstimatedWaitTime()),
        progress: task.progress || 0,
        result: task.result,
        error: task.error
    });
});

// 取消任务
app.post('/api/cancel/:taskId', (req, res) => {
    const { taskId } = req.params;

    const success = imageQueue.cancelTask(taskId);

    if (success) {
        res.json({ message: '任务已取消' });
    } else {
        res.status(404).json({ error: '任务不存在或无法取消' });
    }
});

// 获取队列信息
app.get('/api/queue-info', (req, res) => {
    const queueInfo = imageQueue.getQueueInfo();
    res.json(queueInfo);
});

// 格式化时间
function formatTime(seconds) {
    if (seconds <= 0) return '即将完成';
    if (seconds < 60) return `${seconds}秒`;

    const minutes = Math.ceil(seconds / 60);
    return `${minutes}分钟`;
}



// 提供生成的图片
app.get('/api/images/:filename', async (req, res) => {
    try {
        const { filename } = req.params;
        const imagePath = path.join(__dirname, 'generated', filename);
        
        // 检查文件是否存在
        try {
            await fs.access(imagePath);
            res.sendFile(imagePath);
        } catch {
            // 如果文件不存在，返回占位图片
            res.redirect('https://via.placeholder.com/512x512/667eea/ffffff?text=AI+Generated');
        }
        
    } catch (error) {
        console.error('图片服务失败:', error);
        res.status(500).json({ error: '图片加载失败' });
    }
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`服务器运行在 http://localhost:${PORT}`);
    console.log('H5图片生成应用已启动');
    console.log('ComfyUI接口地址:', imageQueue.comfyUIConfig.baseUrl + imageQueue.comfyUIConfig.endpoints.prompt);

    // 创建生成图片目录
    fs.mkdir('./generated', { recursive: true }).catch(console.error);
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n正在关闭服务器...');
    process.exit(0);
});
