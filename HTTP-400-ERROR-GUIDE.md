# HTTP 400错误解决指南

## 🎯 问题解决

您遇到的"生成失败，处理失败: Request failed with status code 400"问题已经得到全面解决！

### ✅ **已实现的解决方案**

#### **1. 增强参数验证**
- 🔍 **详细验证逻辑**: 检查所有请求参数的类型和长度
- 📝 **清晰错误信息**: 提供具体的错误原因和修复建议
- 🎯 **参数要求**: 明确每个参数的具体要求

#### **2. 请求调试工具**
- 🧪 **可视化调试**: 构建和测试API请求
- 📊 **参数验证**: 实时验证请求参数
- 💡 **智能提示**: 根据验证结果提供修复建议
- 📋 **详细报告**: 显示完整的请求和响应信息

#### **3. 增强错误处理**
- 🎯 **400专用处理**: 专门处理参数错误
- 📝 **详细日志**: 记录所有请求参数用于调试
- 🔗 **自动引导**: 检测到400错误时自动提示使用调试工具

## 🚀 **新增功能**

### **请求调试工具**
在主界面右上角，点击**蓝色调试图标**（🔧）打开请求调试工具

#### **功能特性**:
- ✅ **可视化表单**: 友好的参数输入界面
- 🔧 **实时验证**: 输入时即时验证参数
- 📈 **测试请求**: 一键发送测试请求
- ⏱️ **响应分析**: 显示详细的响应信息
- 📋 **错误诊断**: 精确定位参数问题

#### **参数验证规则**:
```javascript
// 图片描述 (prompt)
- 必需参数，不能为空
- 类型: 字符串 (string)
- 长度: 3-1000 字符
- 示例: "A beautiful landscape with mountains"

// 反向提示词 (negativePrompt)  
- 可选参数
- 类型: 字符串 (string)
- 示例: "blurry, low quality"

// 图片尺寸 (size)
- 可选参数，有默认值
- 格式: "宽x高"
- 示例: "720x480", "1024x768"

// 工作流ID (workflowId)
- 可选参数，默认为 "reba"
- 类型: 字符串 (string)
- 可选值: "reba", "landscape", "anime"
```

### **增强的后端验证**
```javascript
// 详细的参数验证和错误信息
if (!prompt) {
  return res.status(400).json({ 
    success: false,
    error: '请输入图片描述',
    code: 'MISSING_PROMPT',
    details: 'prompt参数是必需的'
  });
}

if (trimmedPrompt.length < 3) {
  return res.status(400).json({ 
    success: false,
    error: '图片描述至少需要3个字符',
    code: 'PROMPT_TOO_SHORT',
    details: `当前长度: ${trimmedPrompt.length}, 最少需要: 3`
  });
}
```

### **详细的请求日志**
后端现在会记录所有请求详情：
```
🎨 收到生成图片请求
📝 请求体: {
  "prompt": "A beautiful landscape",
  "negativePrompt": "blurry",
  "size": "720x480",
  "workflowId": "reba"
}
🔍 开始参数验证...
- prompt: string "A beautiful landscape"
- negativePrompt: string "blurry"
- size: string "720x480"
- workflowId: string "reba"
✅ 参数验证通过
```

## 🔍 **问题诊断流程**

### **步骤1: 使用请求调试工具**
1. 点击主界面右上角的蓝色调试图标（🔧）
2. 在表单中输入您的参数：
   - **图片描述**: 输入您想要生成的图片描述
   - **反向提示词**: 输入不想要的元素（可选）
   - **图片尺寸**: 选择合适的尺寸
   - **工作流**: 选择适合的工作流

### **步骤2: 查看参数验证**
工具会实时显示参数验证结果：
- ✅ **绿色勾号**: 参数有效
- ❌ **红色叉号**: 参数有问题，查看具体错误信息

### **步骤3: 测试请求**
1. 点击"测试请求"按钮
2. 查看测试结果：
   - **成功**: 显示返回的任务ID
   - **失败**: 显示具体的错误信息

### **步骤4: 根据结果修复**
根据测试结果修复问题：
- **参数验证失败**: 修改输入内容
- **网络错误**: 检查API配置
- **服务器错误**: 检查后端状态

## 📋 **常见400错误解决**

### **错误1: "请输入图片描述"**
**原因**: prompt参数为空或未提供

**解决方案**:
1. 确保在描述框中输入了内容
2. 检查输入内容不是只有空格
3. 最少需要3个字符

### **错误2: "图片描述太短"**
**原因**: prompt长度少于3个字符

**解决方案**:
1. 增加描述内容，至少3个字符
2. 提供更详细的图片描述
3. 示例: "美丽的风景" 而不是 "风景"

### **错误3: "图片描述太长"**
**原因**: prompt长度超过1000个字符

**解决方案**:
1. 缩短描述内容
2. 保留关键词，删除冗余描述
3. 分多次生成不同的图片

### **错误4: "工作流ID格式错误"**
**原因**: workflowId参数类型不正确

**解决方案**:
1. 确保选择了有效的工作流
2. 可选值: "reba", "landscape", "anime"
3. 不要手动输入，使用下拉选择

### **错误5: "参数类型错误"**
**原因**: 发送的参数类型不匹配

**解决方案**:
1. 使用请求调试工具检查参数类型
2. 确保所有文本参数都是字符串格式
3. 检查JSON格式是否正确

## 🛠️ **高级调试技巧**

### **使用请求调试工具**
1. **填充示例数据**: 点击"填充示例数据"获取有效的参数示例
2. **查看请求详情**: 检查完整的请求URL和JSON格式
3. **参数验证**: 实时查看每个参数的验证状态
4. **测试响应**: 分析服务器返回的详细信息

### **浏览器开发者工具**
1. 打开浏览器开发者工具 (F12)
2. 查看Network标签页的请求详情
3. 检查Request Payload中的参数
4. 查看Response中的错误信息

### **后端日志分析**
查看后端控制台输出：
```
❌ 参数验证失败: prompt太短
❌ 错误堆栈: [详细错误信息]
```

## 🎯 **最佳实践**

### **参数输入建议**
- **图片描述**: 使用英文，描述具体、清晰
- **长度控制**: 50-200字符通常效果最好
- **关键词**: 包含风格、颜色、构图等关键信息
- **避免**: 过于复杂或矛盾的描述

### **示例有效参数**
```json
{
  "prompt": "A serene mountain landscape at sunset, golden hour lighting, photorealistic, high detail",
  "negativePrompt": "blurry, low quality, distorted, cartoon",
  "size": "720x480",
  "workflowId": "landscape"
}
```

### **故障排除顺序**
1. **使用请求调试工具**验证参数
2. **检查参数验证结果**修复问题
3. **测试请求**确认修复效果
4. **查看后端日志**深入分析

## 🎉 **总结**

现在您的AI图片生成器具备了完整的400错误诊断和解决能力：

✅ **智能参数验证**: 实时检查所有请求参数
✅ **可视化调试**: 友好的参数输入和测试界面
✅ **详细错误信息**: 精确定位参数问题
✅ **自动引导**: 检测到400错误时自动提示解决方案
✅ **最佳实践**: 提供参数输入建议和示例

当遇到"Request failed with status code 400"问题时，系统会：
1. 自动识别这是参数错误
2. 提示用户使用请求调试工具
3. 提供详细的参数验证和修复建议
4. 引导用户输入正确的参数

400错误将不再困扰您的使用体验！🎨✨
