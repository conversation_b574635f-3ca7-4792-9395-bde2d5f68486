# 多阶段构建 Dockerfile
FROM node:18-alpine AS frontend-builder

# 设置工作目录
WORKDIR /app/frontend

# 复制前端依赖文件
COPY frontend/package*.json ./

# 安装前端依赖
RUN npm ci --only=production

# 复制前端源码
COPY frontend/ ./

# 构建前端
RUN npm run build

# 后端阶段
FROM node:18-alpine AS backend

# 设置工作目录
WORKDIR /app

# 安装 PM2
RUN npm install -g pm2

# 复制后端依赖文件
COPY backend/package*.json ./backend/

# 安装后端依赖
WORKDIR /app/backend
RUN npm ci --only=production

# 复制后端源码
WORKDIR /app
COPY backend/ ./backend/

# 复制工作流配置
COPY workflow/ ./workflow/

# 复制前端构建结果
COPY --from=frontend-builder /app/frontend/dist ./backend/dist

# 复制PM2配置
COPY ecosystem.config.js ./

# 创建日志目录
RUN mkdir -p logs

# 暴露端口
EXPOSE 5000

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=5000

# 启动应用
CMD ["pm2-runtime", "start", "ecosystem.config.js", "--env", "production"]
