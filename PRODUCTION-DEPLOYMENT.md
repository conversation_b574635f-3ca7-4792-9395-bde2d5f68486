# AI图片生成器 - 生产环境部署指南

## 📦 项目打包完成

您的AI图片生成器项目已成功打包，可以部署到生产服务器。

### 🎯 打包内容

- **前端**: Vue 3 + Element Plus (已构建为静态文件)
- **后端**: Node.js + Express (包含所有依赖)
- **工作流**: ComfyUI工作流配置文件
- **配置**: 生产环境配置和启动脚本

### 📁 发布文件

```
releases/
├── ai-image-generator-v1.0.0-[timestamp].tar.gz    # 完整部署包
└── ai-image-generator-v1.0.0-[timestamp]-RELEASE-NOTES.md  # 发布说明
```

## 🚀 部署步骤

### 1. 服务器要求

- **操作系统**: Linux/Windows/macOS
- **Node.js**: >= 14.0.0
- **内存**: >= 1GB RAM
- **存储**: >= 2GB 可用空间
- **网络**: 能访问ComfyUI服务

### 2. 上传文件

将 `releases/` 目录中的 `.tar.gz` 文件上传到服务器：

```bash
# 使用scp上传
scp ai-image-generator-v1.0.0-*.tar.gz user@your-server:/path/to/deployment/

# 或使用其他方式上传到服务器
```

### 3. 解压和安装

```bash
# 解压文件
tar -xzf ai-image-generator-v1.0.0-*.tar.gz

# 进入目录
cd ai-image-generator-v1.0.0-*/

# 安装PM2 (推荐)
npm install -g pm2
```

### 4. 配置环境

```bash
# 复制环境变量配置
cp .env.example .env

# 编辑配置文件
nano .env
```

**重要配置项**:
```bash
# 服务端口
PORT=5000

# 运行环境
NODE_ENV=production

# ComfyUI服务地址
COMFYUI_URL=https://your-comfyui-server.com

# CORS允许的域名
CORS_ORIGIN=https://your-domain.com
```

### 5. 启动应用

#### 方式1: 使用启动脚本 (推荐)

```bash
# Linux/Mac
chmod +x start.sh
./start.sh

# Windows
start.bat
```

#### 方式2: 使用PM2

```bash
# 启动应用
pm2 start ecosystem.config.js --env production

# 查看状态
pm2 list

# 查看日志
pm2 logs ai-image-generator
```

#### 方式3: 直接启动

```bash
NODE_ENV=production PORT=5000 node backend/server.js
```

### 6. 验证部署

访问 `http://your-server:5000` 检查应用是否正常运行。

API健康检查: `http://your-server:5000/api/health`

## 🔧 生产环境优化

### 1. 反向代理配置

使用Nginx作为反向代理 (参考 `nginx.conf.example`):

```bash
# 安装Nginx
sudo apt install nginx  # Ubuntu/Debian
sudo yum install nginx  # CentOS/RHEL

# 配置Nginx
sudo cp nginx.conf.example /etc/nginx/sites-available/ai-image-generator
sudo ln -s /etc/nginx/sites-available/ai-image-generator /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 2. SSL证书配置

```bash
# 使用Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 3. 防火墙配置

```bash
# 开放必要端口
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 5000  # 如果不使用反向代理
```

### 4. 进程管理

```bash
# PM2 常用命令
pm2 start ecosystem.config.js --env production  # 启动
pm2 restart ai-image-generator                  # 重启
pm2 stop ai-image-generator                     # 停止
pm2 delete ai-image-generator                   # 删除
pm2 logs ai-image-generator                     # 查看日志
pm2 monit                                       # 监控面板

# 设置开机自启
pm2 startup
pm2 save
```

## 📊 监控和维护

### 1. 日志管理

```bash
# 查看应用日志
tail -f logs/combined.log

# 查看错误日志
tail -f logs/err.log

# 日志轮转 (可选)
sudo apt install logrotate
```

### 2. 性能监控

```bash
# 系统资源监控
htop
free -h
df -h

# PM2 监控
pm2 monit
```

### 3. 备份策略

```bash
# 备份工作流配置
tar -czf workflow-backup-$(date +%Y%m%d).tar.gz workflow/

# 备份日志
tar -czf logs-backup-$(date +%Y%m%d).tar.gz logs/
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   sudo netstat -tlnp | grep :5000
   sudo kill -9 <PID>
   ```

2. **权限问题**
   ```bash
   sudo chown -R $USER:$USER /path/to/app
   chmod +x start.sh
   ```

3. **内存不足**
   ```bash
   # 增加swap空间
   sudo fallocate -l 2G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

4. **ComfyUI连接失败**
   - 检查ComfyUI服务是否运行
   - 验证网络连接
   - 确认URL配置正确

### 日志分析

```bash
# 查看启动日志
pm2 logs ai-image-generator --lines 100

# 查看错误日志
grep -i error logs/err.log

# 实时监控
tail -f logs/combined.log | grep -i error
```

## 📞 技术支持

如遇到部署问题，请提供以下信息：

1. 服务器操作系统和版本
2. Node.js版本 (`node -v`)
3. 错误日志内容
4. 网络环境信息
5. ComfyUI服务状态

## 🎉 部署完成

恭喜！您的AI图片生成器已成功部署到生产环境。

- **前端访问**: `https://your-domain.com`
- **API接口**: `https://your-domain.com/api/`
- **健康检查**: `https://your-domain.com/api/health`

享受您的AI图片生成服务吧！🎨✨
