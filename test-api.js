const axios = require('axios');

async function testAPI() {
    const baseURL = 'http://localhost:3001';
    
    console.log('开始测试API接口...');
    
    try {
        // 1. 测试健康检查
        console.log('\n1. 测试健康检查...');
        const healthResponse = await axios.get(`${baseURL}/health`);
        console.log('健康检查成功:', healthResponse.data);
        
        // 2. 测试基本接口
        console.log('\n2. 测试基本接口...');
        const testResponse = await axios.get(`${baseURL}/test`);
        console.log('基本接口成功:', testResponse.data);
        
        // 3. 测试队列信息
        console.log('\n3. 测试队列信息...');
        const queueResponse = await axios.get(`${baseURL}/api/queue-info`);
        console.log('队列信息成功:', queueResponse.data);
        
        // 4. 测试生成图片API
        console.log('\n4. 测试生成图片API...');
        const generateResponse = await axios.post(`${baseURL}/api/generate`, {
            prompt: 'a beautiful landscape with mountains and sunset',
            style: 'realistic',
            size: '512x512'
        });
        console.log('生成图片API成功:', generateResponse.data);
        
        // 5. 测试状态查询
        console.log('\n5. 测试状态查询...');
        const taskId = generateResponse.data.taskId || 'test-task';
        const statusResponse = await axios.get(`${baseURL}/api/status/${taskId}`);
        console.log('状态查询成功:', statusResponse.data);
        
        console.log('\n✅ 所有API测试通过！');
        
    } catch (error) {
        console.error('\n❌ API测试失败:');
        console.error('错误信息:', error.message);
        
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
        
        if (error.code) {
            console.error('错误代码:', error.code);
        }
    }
}

// 运行测试
testAPI();
