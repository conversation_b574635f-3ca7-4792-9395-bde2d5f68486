{"name": "ai-image-generator", "version": "1.0.0", "description": "AI图片生成器 - Vue前端 + Node.js后端", "scripts": {"install:frontend": "cd frontend && npm install", "install:backend": "cd backend && npm install", "install:all": "npm run install:frontend && npm run install:backend", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build:frontend": "cd frontend && npm run build", "start:backend": "cd backend && npm start", "start": "npm run build:frontend && npm run start:backend"}, "keywords": ["ai", "image-generation", "vue", "nodejs", "comfyui", "workflow"], "author": "AI Assistant", "license": "MIT", "engines": {"node": ">=14.0.0"}}