# AI图片生成器

一个现代化的AI图片生成应用，采用Vue 3前端 + Node.js后端的分离架构，集成ComfyUI API，支持实时图片生成和队列管理。

## 🎯 功能特性

- 🎨 **智能图片生成**: 基于文字描述生成高质量AI图片
- 🚫 **反向提示词**: 支持描述不想要的内容，提高生成质量
- 📱 **现代化界面**: Vue 3 + Element Plus，美观易用
- ⏱️ **实时监控**: 显示生成进度和队列状态
- 🔄 **队列管理**: 支持多任务并发处理
- 📊 **进度追踪**: 实时显示处理进度和预计时间
- 🎯 **参数控制**: 支持多种图片尺寸选择
- 💾 **结果展示**: 高清图片预览和下载功能
- 🔄 **错误处理**: 完善的错误处理和重试机制

## 🛠️ 技术栈

### 前端 (frontend/)
- **Vue 3** + Composition API
- **Element Plus** UI组件库
- **Vite** 构建工具
- **Axios** HTTP客户端
- 响应式设计
- 现代化交互体验

### 后端 (backend/)
- **Node.js** + Express
- **RESTful API** 设计
- **ComfyUI** 集成
- 队列管理系统
- CORS支持
- 错误处理机制

## 📁 项目结构

```
ai-image-generator/
├── frontend/                 # Vue 3 前端应用
│   ├── src/
│   │   ├── App.vue          # 主应用组件
│   │   ├── main.js          # 应用入口
│   │   ├── style.css        # 全局样式
│   │   └── api/
│   │       └── imageApi.js  # API接口封装
│   ├── index.html           # HTML模板
│   ├── vite.config.js       # Vite配置
│   └── package.json         # 前端依赖
├── backend/                 # Node.js 后端API
│   ├── server.js           # Express服务器
│   ├── reba.json           # ComfyUI工作流配置
│   └── package.json        # 后端依赖
├── package.json            # 项目脚本配置
└── README.md              # 项目文档
```

## 🚀 快速开始

### 1. 环境要求

- Node.js >= 14.0.0
- npm >= 6.0.0
- ComfyUI服务 (用于实际图片生成)

### 2. 安装依赖

```bash
# 安装所有依赖
npm run install:all

# 或分别安装
npm run install:frontend
npm run install:backend
```

### 3. 启动应用

```bash
# 开发模式（同时启动前后端）
npm run dev

# 分别启动
npm run dev:frontend  # 前端开发服务器 (端口3000)
npm run dev:backend   # 后端API服务器 (端口3008)

# 生产模式
npm start
```

### 4. 访问应用

- **前端开发**: `http://localhost:3000`
- **后端API**: `http://localhost:3008`
- **生产环境**: `http://localhost:3008`

## 🔧 配置说明

### ComfyUI集成

确保ComfyUI服务正在运行，并更新后端配置中的ComfyUI URL：

```javascript
// backend/server.js
const COMFYUI_URL = 'https://your-comfyui-server.com';
```

### 工作流配置

项目使用 `backend/reba.json` 作为ComfyUI工作流配置，包含：
- 节点27: 正面提示词
- 节点1、20: 反向提示词
- 节点25: 图片尺寸
- 节点26: 随机种子

## 📝 API接口

### 生成图片
```http
POST /api/generate
Content-Type: application/json

{
  "prompt": "图片描述",
  "negativePrompt": "反向提示词",
  "size": "720x480"
}
```

### 查询状态
```http
GET /api/status/{taskId}
```

### 队列信息
```http
GET /api/queue-info
```

### 取消任务
```http
POST /api/cancel/{taskId}
```

## 🎨 使用说明

1. **输入描述**: 在文本框中输入想要生成的图片描述
2. **反向提示词**: 可选，描述不想要的内容
3. **选择尺寸**: 从预设的4种尺寸中选择
4. **开始生成**: 点击生成按钮，系统会返回任务ID
5. **监控进度**: 实时查看生成进度和队列状态
6. **查看结果**: 生成完成后可预览、下载图片

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -ano | findstr :3000
   netstat -ano | findstr :3008
   ```

2. **ComfyUI连接失败**
   - 检查ComfyUI服务是否启动
   - 确认URL配置正确
   - 检查网络连接

3. **依赖安装失败**
   ```bash
   # 清除缓存重新安装
   npm cache clean --force
   npm run install:all
   ```

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

## 📞 支持

如有问题，请创建Issue或联系开发团队。
