version: '3.8'

services:
  ai-image-generator:
    build: .
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
      - COMFYUI_URL=${COMFYUI_URL:-https://your-comfyui-server.com}
    volumes:
      - ./logs:/app/logs
      - ./workflow:/app/workflow
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
