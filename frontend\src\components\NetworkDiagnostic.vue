<template>
  <el-dialog
    v-model="visible"
    title="网络诊断"
    width="700px"
    :close-on-click-modal="false"
  >
    <div class="diagnostic-container">
      <el-alert
        title="网络连接诊断"
        description="检查前端与后端API的网络连接状态"
        type="info"
        :closable="false"
        style="margin-bottom: 20px"
      />
      
      <!-- 快速状态 -->
      <div class="quick-status">
        <el-card shadow="never">
          <template #header>
            <span>快速状态检查</span>
          </template>
          <div class="status-grid">
            <div class="status-item">
              <el-icon :class="networkStatus.online ? 'success-icon' : 'error-icon'">
                <Connection v-if="networkStatus.online" />
                <Close v-else />
              </el-icon>
              <span>网络连接: {{ networkStatus.online ? '在线' : '离线' }}</span>
            </div>
            <div class="status-item" v-if="networkStatus.connection">
              <el-icon class="info-icon"><Monitor /></el-icon>
              <span>连接类型: {{ networkStatus.connection.effectiveType || '未知' }}</span>
            </div>
            <div class="status-item">
              <el-icon class="info-icon"><Timer /></el-icon>
              <span>延迟: {{ networkStatus.connection?.rtt || '未知' }}ms</span>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 诊断按钮 -->
      <div class="diagnostic-actions">
        <el-button 
          type="primary" 
          :loading="diagnosing" 
          @click="runDiagnostic"
          :icon="Search"
        >
          开始诊断
        </el-button>
        <el-button 
          type="success" 
          @click="copyResults"
          :disabled="!diagnosticResults"
          :icon="CopyDocument"
        >
          复制结果
        </el-button>
        <el-button 
          type="warning" 
          @click="clearResults"
          :disabled="!diagnosticResults"
          :icon="Delete"
        >
          清除结果
        </el-button>
      </div>
      
      <!-- 诊断结果 -->
      <div v-if="diagnosticResults" class="diagnostic-results">
        <el-card shadow="never">
          <template #header>
            <span>诊断结果 ({{ diagnosticResults.timestamp }})</span>
          </template>
          
          <!-- API配置信息 -->
          <div class="config-info">
            <h4>当前API配置</h4>
            <el-descriptions :column="2" size="small">
              <el-descriptions-item label="基础地址">
                {{ diagnosticResults.apiConfig.baseURL || '相对路径' }}
              </el-descriptions-item>
              <el-descriptions-item label="超时时间">
                {{ diagnosticResults.apiConfig.timeout || 30000 }}ms
              </el-descriptions-item>
            </el-descriptions>
          </div>
          
          <!-- 测试结果 -->
          <div class="test-results">
            <h4>连接测试结果</h4>
            <div 
              v-for="(test, index) in diagnosticResults.tests" 
              :key="index"
              class="test-item"
            >
              <div class="test-header">
                <el-icon :class="test.status === 'success' ? 'success-icon' : 'error-icon'">
                  <Check v-if="test.status === 'success'" />
                  <Close v-else />
                </el-icon>
                <span class="test-name">{{ test.name }}</span>
                <el-tag 
                  :type="test.status === 'success' ? 'success' : 'danger'" 
                  size="small"
                >
                  {{ test.status === 'success' ? '成功' : '失败' }}
                </el-tag>
              </div>
              <div class="test-details">
                <p><strong>URL:</strong> {{ test.url }}</p>
                <p><strong>结果:</strong> {{ test.details }}</p>
              </div>
            </div>
          </div>
          
          <!-- 建议 -->
          <div class="suggestions">
            <h4>问题解决建议</h4>
            <el-alert
              v-if="hasFailedTests"
              title="检测到连接问题"
              type="warning"
              :closable="false"
            >
              <ul>
                <li>检查后端服务器是否正常运行</li>
                <li>确认API地址配置是否正确</li>
                <li>检查防火墙和网络设置</li>
                <li>尝试刷新页面或重新配置API</li>
              </ul>
            </el-alert>
            <el-alert
              v-else-if="diagnosticResults.tests.length > 0"
              title="网络连接正常"
              type="success"
              :closable="false"
            >
              所有连接测试都通过了，网络状态良好。
            </el-alert>
          </div>
        </el-card>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Connection, 
  Close, 
  Monitor, 
  Timer, 
  Search, 
  CopyDocument, 
  Delete,
  Check
} from '@element-plus/icons-vue'
import { networkDiagnostic, getNetworkStatus } from '../api/imageApi.js'

export default {
  name: 'NetworkDiagnostic',
  components: {
    Connection,
    Close,
    Monitor,
    Timer,
    Search,
    CopyDocument,
    Delete,
    Check
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const visible = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value)
    })
    
    const diagnosing = ref(false)
    const diagnosticResults = ref(null)
    const networkStatus = reactive({
      online: navigator.onLine,
      connection: null
    })
    
    const hasFailedTests = computed(() => {
      return diagnosticResults.value?.tests?.some(test => test.status === 'failed') || false
    })
    
    // 更新网络状态
    const updateNetworkStatus = () => {
      const status = getNetworkStatus()
      Object.assign(networkStatus, status)
    }
    
    // 运行诊断
    const runDiagnostic = async () => {
      diagnosing.value = true
      
      try {
        ElMessage.info('正在进行网络诊断...')
        const results = await networkDiagnostic()
        diagnosticResults.value = results
        
        const failedCount = results.tests.filter(test => test.status === 'failed').length
        if (failedCount === 0) {
          ElMessage.success('网络诊断完成，所有测试通过')
        } else {
          ElMessage.warning(`网络诊断完成，发现 ${failedCount} 个问题`)
        }
      } catch (error) {
        ElMessage.error(`诊断失败: ${error.message}`)
        console.error('网络诊断失败:', error)
      } finally {
        diagnosing.value = false
      }
    }
    
    // 复制结果
    const copyResults = async () => {
      if (!diagnosticResults.value) return
      
      try {
        const text = JSON.stringify(diagnosticResults.value, null, 2)
        await navigator.clipboard.writeText(text)
        ElMessage.success('诊断结果已复制到剪贴板')
      } catch (error) {
        ElMessage.error('复制失败')
      }
    }
    
    // 清除结果
    const clearResults = () => {
      diagnosticResults.value = null
      ElMessage.info('诊断结果已清除')
    }
    
    // 关闭对话框
    const handleClose = () => {
      visible.value = false
    }
    
    // 监听网络状态变化
    const handleOnline = () => {
      networkStatus.online = true
      ElMessage.success('网络已连接')
    }
    
    const handleOffline = () => {
      networkStatus.online = false
      ElMessage.warning('网络已断开')
    }
    
    // 监听对话框打开
    watch(visible, (newVal) => {
      if (newVal) {
        updateNetworkStatus()
      }
    })
    
    onMounted(() => {
      updateNetworkStatus()
      window.addEventListener('online', handleOnline)
      window.addEventListener('offline', handleOffline)
    })
    
    return {
      visible,
      diagnosing,
      diagnosticResults,
      networkStatus,
      hasFailedTests,
      runDiagnostic,
      copyResults,
      clearResults,
      handleClose
    }
  }
}
</script>

<style scoped>
.diagnostic-container {
  padding: 10px 0;
}

.quick-status {
  margin-bottom: 20px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
}

.diagnostic-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  justify-content: center;
}

.config-info, .test-results, .suggestions {
  margin-bottom: 20px;
}

.config-info h4, .test-results h4, .suggestions h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
}

.test-item {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 10px;
}

.test-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.test-name {
  font-weight: 500;
  flex: 1;
}

.test-details {
  font-size: 12px;
  color: #606266;
}

.test-details p {
  margin: 2px 0;
}

.success-icon {
  color: #67c23a;
}

.error-icon {
  color: #f56c6c;
}

.info-icon {
  color: #409eff;
}

.suggestions ul {
  margin: 10px 0;
  padding-left: 20px;
}

.suggestions li {
  margin: 5px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
