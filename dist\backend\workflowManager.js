const fs = require('fs').promises;
const path = require('path');

class WorkflowManager {
  constructor() {
    this.workflowDir = path.join(__dirname, '../workflow');
    this.cache = new Map(); // 缓存已加载的工作流
  }

  /**
   * 获取所有可用的工作流
   * @returns {Promise<Array>} 工作流列表
   */
  async getAvailableWorkflows() {
    try {
      const files = await fs.readdir(this.workflowDir);
      const workflows = files
        .filter(file => file.endsWith('.json'))
        .map(file => ({
          id: path.basename(file, '.json'),
          name: this.getWorkflowDisplayName(file),
          filename: file,
          path: path.join(this.workflowDir, file)
        }));
      
      console.log('可用工作流:', workflows.map(w => w.name));
      return workflows;
    } catch (error) {
      console.error('获取工作流列表失败:', error);
      return [];
    }
  }

  /**
   * 加载指定的工作流
   * @param {string} workflowId - 工作流ID
   * @returns {Promise<Object>} 工作流配置
   */
  async loadWorkflow(workflowId = 'reba') {
    try {
      // 检查缓存
      if (this.cache.has(workflowId)) {
        console.log(`从缓存加载工作流: ${workflowId}`);
        return JSON.parse(JSON.stringify(this.cache.get(workflowId))); // 深拷贝
      }

      const workflowPath = path.join(this.workflowDir, `${workflowId}.json`);
      console.log(`加载工作流文件: ${workflowPath}`);
      
      const workflowData = await fs.readFile(workflowPath, 'utf8');
      const workflow = JSON.parse(workflowData);
      
      // 缓存工作流
      this.cache.set(workflowId, workflow);
      console.log(`工作流加载成功: ${workflowId}`);
      
      return JSON.parse(JSON.stringify(workflow)); // 返回深拷贝
    } catch (error) {
      console.error(`加载工作流失败 (${workflowId}):`, error.message);
      throw new Error(`工作流文件不存在或格式错误: ${workflowId}.json`);
    }
  }

  /**
   * 更新工作流参数
   * @param {Object} workflow - 工作流配置
   * @param {Object} params - 参数对象
   * @param {string} params.prompt - 正面提示词
   * @param {string} params.negativePrompt - 反向提示词
   * @param {string} params.size - 图片尺寸
   * @param {string} params.workflowId - 工作流ID
   * @returns {Object} 更新后的工作流
   */
  updateWorkflowParams(workflow, params) {
    const { prompt, negativePrompt, size, workflowId = 'reba' } = params;
    
    console.log(`更新工作流参数 (${workflowId}):`, {
      prompt: prompt?.substring(0, 50) + '...',
      negativePrompt: negativePrompt?.substring(0, 30) + '...',
      size
    });

    // 根据不同工作流类型更新参数
    switch (workflowId) {
      case 'reba':
        this.updateRebaWorkflow(workflow, params);
        break;
      default:
        // 通用更新逻辑
        this.updateGenericWorkflow(workflow, params);
    }

    return workflow;
  }

  /**
   * 更新Reba工作流参数
   * @param {Object} workflow - 工作流配置
   * @param {Object} params - 参数对象
   */
  updateRebaWorkflow(workflow, params) {
    const { prompt, negativePrompt, size } = params;

    // 更新正面提示词 (节点27)
    if (workflow['27'] && workflow['27'].inputs) {
      workflow['27'].inputs.text = `best quality, masterpiece, ${prompt}`;
      console.log('✅ 正面提示词已更新 (节点27)');
    }

    // 更新负面提示词 (节点1和20)
    const finalNegativePrompt = negativePrompt ? 
      `${negativePrompt}, worst quality, low quality, blurry, bad anatomy` : 
      'worst quality, low quality, blurry, bad anatomy';
      
    if (workflow['1'] && workflow['1'].inputs) {
      workflow['1'].inputs.text = finalNegativePrompt;
      console.log('✅ 负面提示词已更新 (节点1)');
    }
    if (workflow['20'] && workflow['20'].inputs) {
      workflow['20'].inputs.text = finalNegativePrompt;
      console.log('✅ 负面提示词已更新 (节点20)');
    }

    // 更新图片尺寸 (节点25)
    if (size && workflow['25'] && workflow['25'].inputs) {
      const [width, height] = size.split('x').map(Number);
      workflow['25'].inputs.width = width;
      workflow['25'].inputs.height = height;
      workflow['25'].inputs.batch_size = 1;
      console.log(`✅ 图片尺寸已更新 (节点25): ${width}x${height}`);
    }

    // 生成随机种子 (节点26)
    const seed = Math.floor(Math.random() * 1000000000);
    if (workflow['26'] && workflow['26'].inputs) {
      workflow['26'].inputs.seed = seed;
      console.log('✅ 随机种子已生成 (节点26):', seed);
    }
  }

  /**
   * 通用工作流参数更新
   * @param {Object} workflow - 工作流配置
   * @param {Object} params - 参数对象
   */
  updateGenericWorkflow(workflow, params) {
    const { prompt, negativePrompt, size } = params;
    
    // 查找可能的提示词节点
    for (const nodeId in workflow) {
      const node = workflow[nodeId];
      if (node.class_type === 'CLIPTextEncode' && node.inputs && node.inputs.text) {
        // 简单的启发式判断：包含负面词汇的可能是负面提示词
        const isNegative = node.inputs.text.toLowerCase().includes('worst') || 
                          node.inputs.text.toLowerCase().includes('bad') ||
                          node.inputs.text.toLowerCase().includes('low quality');
        
        if (isNegative && negativePrompt) {
          node.inputs.text = `${negativePrompt}, worst quality, low quality`;
          console.log(`✅ 负面提示词已更新 (节点${nodeId})`);
        } else if (!isNegative && prompt) {
          node.inputs.text = `best quality, ${prompt}`;
          console.log(`✅ 正面提示词已更新 (节点${nodeId})`);
        }
      }
      
      // 查找尺寸节点
      if (node.class_type === 'EmptyLatentImage' && node.inputs && size) {
        const [width, height] = size.split('x').map(Number);
        node.inputs.width = width;
        node.inputs.height = height;
        console.log(`✅ 图片尺寸已更新 (节点${nodeId}): ${width}x${height}`);
      }
      
      // 查找采样器节点更新种子
      if (node.class_type === 'KSampler' && node.inputs) {
        const seed = Math.floor(Math.random() * 1000000000);
        node.inputs.seed = seed;
        console.log(`✅ 随机种子已生成 (节点${nodeId}):`, seed);
      }
    }
  }

  /**
   * 获取工作流显示名称
   * @param {string} filename - 文件名
   * @returns {string} 显示名称
   */
  getWorkflowDisplayName(filename) {
    const nameMap = {
      'reba.json': 'Reba人像生成',
      'landscape.json': '风景生成',
      'anime.json': '动漫风格',
      'realistic.json': '写实风格'
    };
    
    return nameMap[filename] || path.basename(filename, '.json');
  }

  /**
   * 验证工作流文件
   * @param {string} workflowId - 工作流ID
   * @returns {Promise<boolean>} 是否有效
   */
  async validateWorkflow(workflowId) {
    try {
      const workflow = await this.loadWorkflow(workflowId);
      
      // 基本验证
      if (!workflow || typeof workflow !== 'object') {
        return false;
      }
      
      // 检查是否有节点
      const nodeCount = Object.keys(workflow).length;
      if (nodeCount === 0) {
        return false;
      }
      
      console.log(`工作流验证通过: ${workflowId} (${nodeCount}个节点)`);
      return true;
    } catch (error) {
      console.error(`工作流验证失败: ${workflowId}`, error.message);
      return false;
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache.clear();
    console.log('工作流缓存已清除');
  }

  /**
   * 获取工作流信息
   * @param {string} workflowId - 工作流ID
   * @returns {Promise<Object>} 工作流信息
   */
  async getWorkflowInfo(workflowId) {
    try {
      const workflow = await this.loadWorkflow(workflowId);
      const nodeCount = Object.keys(workflow).length;
      
      // 分析节点类型
      const nodeTypes = {};
      for (const nodeId in workflow) {
        const nodeType = workflow[nodeId].class_type;
        nodeTypes[nodeType] = (nodeTypes[nodeType] || 0) + 1;
      }
      
      return {
        id: workflowId,
        nodeCount,
        nodeTypes,
        hasTextEncode: Object.values(workflow).some(node => node.class_type === 'CLIPTextEncode'),
        hasImageGeneration: Object.values(workflow).some(node => node.class_type === 'KSampler'),
        hasSaveImage: Object.values(workflow).some(node => node.class_type === 'SaveImage')
      };
    } catch (error) {
      throw new Error(`获取工作流信息失败: ${error.message}`);
    }
  }
}

module.exports = WorkflowManager;
