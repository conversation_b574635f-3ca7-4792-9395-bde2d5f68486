<template>
  <el-dialog
    v-model="visible"
    title="API配置"
    width="500px"
    :close-on-click-modal="false"
  >
    <div class="config-container">
      <el-alert
        title="配置API服务器地址"
        description="如果后端服务器地址发生变化，请在此更新配置"
        type="info"
        :closable="false"
        style="margin-bottom: 20px"
      />
      
      <el-form :model="configForm" label-width="120px">
        <el-form-item label="API基础地址">
          <el-input
            v-model="configForm.baseURL"
            placeholder="例如: http://localhost:4001"
            clearable
          >
            <template #prepend>
              <el-icon><Link /></el-icon>
            </template>
          </el-input>
          <div class="form-hint">
            请输入完整的服务器地址，包含协议和端口
          </div>
        </el-form-item>
        
        <el-form-item label="连接测试">
          <el-button 
            type="primary" 
            :loading="testing" 
            @click="testConnection"
            :disabled="!configForm.baseURL"
          >
            <el-icon><Connection /></el-icon>
            测试连接
          </el-button>
          <span v-if="testResult" :class="testResultClass" style="margin-left: 10px;">
            {{ testResult }}
          </span>
        </el-form-item>
      </el-form>
      
      <div class="current-config">
        <h4>当前配置</h4>
        <p><strong>API地址:</strong> {{ currentBaseURL }}</p>
        <p><strong>状态:</strong> 
          <el-tag :type="isConnected ? 'success' : 'danger'" size="small">
            {{ isConnected ? '已连接' : '未连接' }}
          </el-tag>
        </p>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button @click="handleReset" type="warning">重置为默认</el-button>
        <el-button 
          type="primary" 
          @click="handleSave"
          :disabled="!configForm.baseURL || testing"
        >
          保存配置
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Link, Connection } from '@element-plus/icons-vue'
import { getApiConfig, setApiConfig, testApiConnection } from '../utils/apiConfig.js'

export default {
  name: 'ApiConfig',
  components: {
    Link,
    Connection
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'config-changed'],
  setup(props, { emit }) {
    const visible = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value)
    })
    
    const configForm = reactive({
      baseURL: ''
    })
    
    const testing = ref(false)
    const testResult = ref('')
    const isConnected = ref(false)
    
    const currentBaseURL = computed(() => {
      return getApiConfig().baseURL || '未配置'
    })
    
    const testResultClass = computed(() => {
      if (testResult.value.includes('成功')) {
        return 'test-success'
      } else if (testResult.value.includes('失败')) {
        return 'test-error'
      }
      return ''
    })
    
    // 监听对话框打开，加载当前配置
    watch(visible, (newVal) => {
      if (newVal) {
        loadCurrentConfig()
      }
    })
    
    const loadCurrentConfig = () => {
      const config = getApiConfig()
      configForm.baseURL = config.baseURL || ''
      checkCurrentConnection()
    }
    
    const checkCurrentConnection = async () => {
      try {
        const connected = await testApiConnection()
        isConnected.value = connected
      } catch (error) {
        isConnected.value = false
      }
    }
    
    const testConnection = async () => {
      if (!configForm.baseURL) {
        ElMessage.warning('请先输入API地址')
        return
      }
      
      testing.value = true
      testResult.value = ''
      
      try {
        // 临时设置API地址进行测试
        const originalConfig = getApiConfig()
        setApiConfig({ baseURL: configForm.baseURL })
        
        const connected = await testApiConnection()
        
        if (connected) {
          testResult.value = '✅ 连接成功'
          ElMessage.success('API连接测试成功')
        } else {
          testResult.value = '❌ 连接失败'
          ElMessage.error('API连接测试失败')
          // 恢复原配置
          setApiConfig(originalConfig)
        }
      } catch (error) {
        testResult.value = `❌ 连接失败: ${error.message}`
        ElMessage.error(`连接测试失败: ${error.message}`)
        // 恢复原配置
        const originalConfig = getApiConfig()
        setApiConfig(originalConfig)
      } finally {
        testing.value = false
      }
    }
    
    const handleSave = () => {
      if (!configForm.baseURL) {
        ElMessage.warning('请输入API地址')
        return
      }
      
      // 保存配置
      setApiConfig({
        baseURL: configForm.baseURL.replace(/\/$/, '') // 移除末尾斜杠
      })
      
      ElMessage.success('API配置已保存')
      emit('config-changed')
      visible.value = false
    }
    
    const handleCancel = () => {
      visible.value = false
    }
    
    const handleReset = () => {
      configForm.baseURL = ''
      setApiConfig({ baseURL: '' })
      ElMessage.info('已重置为默认配置')
      emit('config-changed')
    }
    
    return {
      visible,
      configForm,
      testing,
      testResult,
      testResultClass,
      currentBaseURL,
      isConnected,
      testConnection,
      handleSave,
      handleCancel,
      handleReset
    }
  }
}
</script>

<style scoped>
.config-container {
  padding: 10px 0;
}

.form-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.current-config {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  margin-top: 20px;
}

.current-config h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.current-config p {
  margin: 5px 0;
  color: #606266;
}

.test-success {
  color: #67c23a;
  font-weight: 500;
}

.test-error {
  color: #f56c6c;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
