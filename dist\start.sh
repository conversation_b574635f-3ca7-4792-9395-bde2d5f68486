#!/bin/bash
echo "启动AI图片生成器..."

# 检查Node.js版本
node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$node_version" -lt "14" ]; then
    echo "错误: 需要Node.js 14或更高版本"
    exit 1
fi

# 设置环境变量
export NODE_ENV=production
export PORT=${PORT:-5000}

# 创建日志目录
mkdir -p logs

# 启动应用
if command -v pm2 &> /dev/null; then
    echo "使用PM2启动..."
    pm2 start ecosystem.config.js --env production
else
    echo "使用Node.js直接启动..."
    node backend/server.js
fi
