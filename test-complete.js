const axios = require('axios');

async function testCompleteWorkflow() {
    const baseURL = 'http://localhost:3002';
    
    console.log('🚀 开始完整工作流测试...\n');
    
    try {
        // 1. 健康检查
        console.log('1️⃣ 健康检查...');
        const healthResponse = await axios.get(`${baseURL}/health`);
        console.log('✅ 健康检查成功:', healthResponse.data);
        
        // 2. 测试生成图片（包含反向提示词）
        console.log('\n2️⃣ 测试生成图片（新参数）...');
        const generateResponse = await axios.post(`${baseURL}/api/generate`, {
            prompt: 'a beautiful landscape with mountains and sunset, peaceful scene',
            negativePrompt: 'blurry, low quality, distorted, ugly',
            size: '720x480'
        });
        console.log('✅ 生成请求成功:', generateResponse.data);
        
        const taskId = generateResponse.data.taskId;
        console.log(`📋 任务ID: ${taskId}`);
        
        // 3. 监控任务状态和进度
        console.log('\n3️⃣ 监控任务状态和进度...');
        let attempts = 0;
        const maxAttempts = 30; // 最多检查30次（2.5分钟）
        
        while (attempts < maxAttempts) {
            attempts++;
            
            try {
                const statusResponse = await axios.get(`${baseURL}/api/status/${taskId}`);
                const status = statusResponse.data;
                
                console.log(`📊 第${attempts}次检查:`);
                console.log(`   状态: ${status.status}`);
                console.log(`   进度: ${status.progress}%`);
                console.log(`   预计时间: ${status.estimatedTime}`);
                
                if (status.status === 'completed') {
                    console.log('\n🎉 任务完成!');
                    console.log('📸 生成结果:');
                    console.log(`   图片URL: ${status.result.imageUrl}`);
                    console.log(`   文件名: ${status.result.filename}`);
                    console.log(`   正面提示词: ${status.result.prompt}`);
                    console.log(`   反向提示词: ${status.result.negativePrompt}`);
                    console.log(`   图片尺寸: ${status.result.size}`);
                    console.log(`   生成时间: ${status.result.generatedAt}`);
                    
                    // 测试图片URL是否可访问
                    console.log('\n4️⃣ 测试图片URL访问...');
                    try {
                        const imageResponse = await axios.head(status.result.imageUrl, { timeout: 10000 });
                        console.log('✅ 图片URL可访问，状态码:', imageResponse.status);
                    } catch (imageError) {
                        console.log('⚠️ 图片URL访问失败:', imageError.message);
                    }
                    
                    break;
                } else if (status.status === 'failed') {
                    console.log('\n❌ 任务失败:', status.error);
                    break;
                } else {
                    console.log(`   ⏳ 继续等待...`);
                    await new Promise(resolve => setTimeout(resolve, 5000)); // 等待5秒
                }
                
            } catch (statusError) {
                console.log(`⚠️ 状态查询失败: ${statusError.message}`);
                break;
            }
        }
        
        if (attempts >= maxAttempts) {
            console.log('\n⏰ 达到最大检查次数，停止监控');
        }
        
        // 5. 测试队列信息
        console.log('\n5️⃣ 测试队列信息...');
        const queueResponse = await axios.get(`${baseURL}/api/queue-info`);
        console.log('✅ 队列信息:', queueResponse.data);
        
        console.log('\n🏁 完整工作流测试完成!');
        
    } catch (error) {
        console.error('\n❌ 测试失败:');
        console.error('错误信息:', error.message);
        
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
        
        throw error;
    }
}

// 运行测试
if (require.main === module) {
    testCompleteWorkflow()
        .then(() => {
            console.log('\n✨ 所有测试通过!');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n💥 测试失败');
            process.exit(1);
        });
}

module.exports = testCompleteWorkflow;
