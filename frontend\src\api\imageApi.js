import axios from 'axios'
import { getApiConfig, getApiUrl } from '../utils/apiConfig.js'

// 创建axios实例
const createApiInstance = () => {
  const config = getApiConfig()

  return axios.create({
    baseURL: config.baseURL || '/api',
    timeout: config.timeout || 30000,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 获取当前API实例
let api = createApiInstance()

// 监听配置变更，重新创建API实例
window.addEventListener('api-config-changed', () => {
  console.log('API配置已变更，重新创建API实例')
  api = createApiInstance()
})

// 设置拦截器的函数
const setupInterceptors = (apiInstance) => {
  // 请求拦截器
  apiInstance.interceptors.request.use(
    config => {
      console.log('发送请求:', config.method?.toUpperCase(), config.url, config.data)
      return config
    },
    error => {
      console.error('请求错误:', error)
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  apiInstance.interceptors.response.use(
    response => {
      console.log('收到响应:', response.status, response.data)
      return response.data
    },
    error => {
      console.error('响应错误:', error.response?.status, error.response?.data || error.message)

      // 统一错误处理
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.message ||
                          error.message ||
                          '网络请求失败'

      return Promise.reject(new Error(errorMessage))
    }
  )
}

// 为当前API实例设置拦截器
setupInterceptors(api)

// 重新创建API实例时也要设置拦截器
window.addEventListener('api-config-changed', () => {
  setupInterceptors(api)
})

/**
 * 获取当前API实例（动态获取以支持配置变更）
 * @returns {Object} axios实例
 */
const getApi = () => api

/**
 * 生成图片
 * @param {Object} params - 生成参数
 * @param {string} params.prompt - 正面提示词
 * @param {string} params.negativePrompt - 反向提示词
 * @param {string} params.size - 图片尺寸
 * @param {string} params.workflowId - 工作流ID
 * @returns {Promise<Object>} 任务信息
 */
export const generateImage = async (params) => {
  return await getApi().post('/generate', params)
}

/**
 * 获取任务状态
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object>} 任务状态
 */
export const getTaskStatus = async (taskId) => {
  return await getApi().get(`/status/${taskId}`)
}

/**
 * 取消任务
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object>} 取消结果
 */
export const cancelTask = async (taskId) => {
  return await getApi().post(`/cancel/${taskId}`)
}

/**
 * 获取队列信息
 * @returns {Promise<Object>} 队列信息
 */
export const getQueueInfo = async () => {
  return await getApi().get('/queue-info')
}

/**
 * 获取可用工作流列表
 * @returns {Promise<Object>} 工作流列表
 */
export const getWorkflows = async () => {
  return await getApi().get('/workflows')
}

/**
 * 获取工作流信息
 * @param {string} workflowId - 工作流ID
 * @returns {Promise<Object>} 工作流信息
 */
export const getWorkflowInfo = async (workflowId) => {
  return await getApi().get(`/workflows/${workflowId}`)
}

/**
 * 健康检查
 * @returns {Promise<Object>} 健康状态
 */
export const healthCheck = async () => {
  return await getApi().get('/health')
}

export default getApi
