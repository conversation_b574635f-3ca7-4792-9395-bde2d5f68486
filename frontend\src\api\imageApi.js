import axios from 'axios'
import { getApiConfig, getApiUrl } from '../utils/apiConfig.js'

// 创建axios实例
const createApiInstance = () => {
  const config = getApiConfig()

  // 确定baseURL
  let baseURL
  if (config.baseURL) {
    // 如果配置了baseURL，确保以/api结尾
    baseURL = config.baseURL.replace(/\/$/, '') + '/api'
  } else {
    // 如果没有配置baseURL，使用相对路径
    baseURL = '/api'
  }

  console.log('创建API实例，baseURL:', baseURL)

  return axios.create({
    baseURL: baseURL,
    timeout: config.timeout || 30000,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 获取当前API实例
let api = createApiInstance()

// 监听配置变更，重新创建API实例
window.addEventListener('api-config-changed', () => {
  console.log('API配置已变更，重新创建API实例')
  api = createApiInstance()
})

// 设置拦截器的函数
const setupInterceptors = (apiInstance) => {
  // 请求拦截器
  apiInstance.interceptors.request.use(
    config => {
      // 构建完整URL用于调试
      const fullUrl = config.baseURL + config.url
      console.log('发送请求:', config.method?.toUpperCase(), fullUrl, config.data)
      console.log('请求配置:', {
        baseURL: config.baseURL,
        url: config.url,
        method: config.method,
        headers: config.headers
      })
      return config
    },
    error => {
      console.error('请求错误:', error)
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  apiInstance.interceptors.response.use(
    response => {
      console.log('收到响应:', response.status, response.data)
      return response.data
    },
    error => {
      console.error('响应错误:', error.response?.status, error.response?.data || error.message)

      // 详细的错误诊断
      let errorMessage = '网络请求失败'
      let errorDetails = ''

      if (error.code === 'NETWORK_ERROR' || error.message === 'Network Error') {
        errorMessage = '网络连接失败，请检查网络连接'
        errorDetails = '可能原因：1. 网络断开 2. 服务器未启动 3. 防火墙阻止 4. API地址配置错误'
      } else if (error.code === 'ECONNREFUSED') {
        errorMessage = '连接被拒绝，服务器可能未启动'
        errorDetails = '请确认后端服务器是否正常运行'
      } else if (error.code === 'TIMEOUT' || error.code === 'ECONNABORTED') {
        errorMessage = '请求超时，请稍后重试'
        errorDetails = '网络较慢或服务器响应时间过长'
      } else if (error.response) {
        // 服务器返回了错误状态码
        const status = error.response.status
        if (status === 404) {
          errorMessage = 'API接口不存在 (404)'
          errorDetails = '请检查API地址配置是否正确'
        } else if (status === 500) {
          errorMessage = '服务器内部错误 (500)'
          errorDetails = error.response.data?.error || '服务器处理请求时发生错误'
        } else if (status === 403) {
          errorMessage = '访问被禁止 (403)'
          errorDetails = '可能是CORS配置问题或权限不足'
        } else {
          errorMessage = error.response.data?.error ||
                        error.response.data?.message ||
                        `HTTP错误 ${status}`
          errorDetails = error.response.data?.details || ''
        }
      } else {
        errorMessage = error.message || '未知网络错误'
        errorDetails = '请检查网络连接和API配置'
      }

      // 输出详细错误信息到控制台
      console.error('详细错误信息:', {
        message: errorMessage,
        details: errorDetails,
        code: error.code,
        status: error.response?.status,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          baseURL: error.config?.baseURL
        }
      })

      const enhancedError = new Error(errorMessage)
      enhancedError.details = errorDetails
      enhancedError.originalError = error

      return Promise.reject(enhancedError)
    }
  )
}

// 为当前API实例设置拦截器
setupInterceptors(api)

// 重新创建API实例时也要设置拦截器
window.addEventListener('api-config-changed', () => {
  setupInterceptors(api)
})

/**
 * 获取当前API实例（动态获取以支持配置变更）
 * @returns {Object} axios实例
 */
const getApi = () => api

/**
 * 生成图片
 * @param {Object} params - 生成参数
 * @param {string} params.prompt - 正面提示词
 * @param {string} params.negativePrompt - 反向提示词
 * @param {string} params.size - 图片尺寸
 * @param {string} params.workflowId - 工作流ID
 * @returns {Promise<Object>} 任务信息
 */
export const generateImage = async (params) => {
  return await getApi().post('/generate', params)
}

/**
 * 获取任务状态
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object>} 任务状态
 */
export const getTaskStatus = async (taskId) => {
  return await getApi().get(`/status/${taskId}`)
}

/**
 * 取消任务
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object>} 取消结果
 */
export const cancelTask = async (taskId) => {
  return await getApi().post(`/cancel/${taskId}`)
}

/**
 * 获取队列信息
 * @returns {Promise<Object>} 队列信息
 */
export const getQueueInfo = async () => {
  return await getApi().get('/queue-info')
}

/**
 * 获取可用工作流列表
 * @returns {Promise<Object>} 工作流列表
 */
export const getWorkflows = async () => {
  return await getApi().get('/workflows')
}

/**
 * 获取工作流信息
 * @param {string} workflowId - 工作流ID
 * @returns {Promise<Object>} 工作流信息
 */
export const getWorkflowInfo = async (workflowId) => {
  return await getApi().get(`/workflows/${workflowId}`)
}

/**
 * 健康检查
 * @returns {Promise<Object>} 健康状态
 */
export const healthCheck = async () => {
  return await getApi().get('/health')
}

/**
 * 网络诊断工具
 * @returns {Promise<Object>} 诊断结果
 */
export const networkDiagnostic = async () => {
  const config = getApiConfig()
  const results = {
    timestamp: new Date().toISOString(),
    apiConfig: config,
    tests: []
  }

  // 测试1: 基础连通性
  try {
    const response = await fetch('/api/health', {
      method: 'GET',
      timeout: 5000
    })
    results.tests.push({
      name: '基础API连通性',
      status: response.ok ? 'success' : 'failed',
      details: `HTTP ${response.status}`,
      url: '/api/health'
    })
  } catch (error) {
    results.tests.push({
      name: '基础API连通性',
      status: 'failed',
      details: error.message,
      url: '/api/health'
    })
  }

  // 测试2: 配置的API地址
  if (config.baseURL) {
    try {
      const response = await fetch(`${config.baseURL}/health`, {
        method: 'GET',
        timeout: 5000
      })
      results.tests.push({
        name: '配置的API地址',
        status: response.ok ? 'success' : 'failed',
        details: `HTTP ${response.status}`,
        url: `${config.baseURL}/health`
      })
    } catch (error) {
      results.tests.push({
        name: '配置的API地址',
        status: 'failed',
        details: error.message,
        url: `${config.baseURL}/health`
      })
    }
  }

  // 测试3: CORS预检
  try {
    const response = await fetch('/api/health', {
      method: 'OPTIONS',
      timeout: 5000
    })
    results.tests.push({
      name: 'CORS预检',
      status: response.ok ? 'success' : 'failed',
      details: `HTTP ${response.status}`,
      url: '/api/health (OPTIONS)'
    })
  } catch (error) {
    results.tests.push({
      name: 'CORS预检',
      status: 'failed',
      details: error.message,
      url: '/api/health (OPTIONS)'
    })
  }

  return results
}

/**
 * 获取网络状态信息
 * @returns {Object} 网络状态
 */
export const getNetworkStatus = () => {
  return {
    online: navigator.onLine,
    connection: navigator.connection ? {
      effectiveType: navigator.connection.effectiveType,
      downlink: navigator.connection.downlink,
      rtt: navigator.connection.rtt
    } : null,
    userAgent: navigator.userAgent,
    language: navigator.language,
    cookieEnabled: navigator.cookieEnabled
  }
}

export default getApi
