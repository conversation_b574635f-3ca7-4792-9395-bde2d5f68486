import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    console.log('发送请求:', config.method?.toUpperCase(), config.url, config.data)
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('收到响应:', response.status, response.data)
    return response.data
  },
  error => {
    console.error('响应错误:', error.response?.status, error.response?.data || error.message)
    
    // 统一错误处理
    const errorMessage = error.response?.data?.error || 
                        error.response?.data?.message || 
                        error.message || 
                        '网络请求失败'
    
    return Promise.reject(new Error(errorMessage))
  }
)

/**
 * 生成图片
 * @param {Object} params - 生成参数
 * @param {string} params.prompt - 正面提示词
 * @param {string} params.negativePrompt - 反向提示词
 * @param {string} params.size - 图片尺寸
 * @returns {Promise<Object>} 任务信息
 */
export const generateImage = async (params) => {
  return await api.post('/generate', params)
}

/**
 * 获取任务状态
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object>} 任务状态
 */
export const getTaskStatus = async (taskId) => {
  return await api.get(`/status/${taskId}`)
}

/**
 * 取消任务
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object>} 取消结果
 */
export const cancelTask = async (taskId) => {
  return await api.post(`/cancel/${taskId}`)
}

/**
 * 获取队列信息
 * @returns {Promise<Object>} 队列信息
 */
export const getQueueInfo = async () => {
  return await api.get('/queue-info')
}

/**
 * 获取可用工作流列表
 * @returns {Promise<Object>} 工作流列表
 */
export const getWorkflows = async () => {
  return await api.get('/workflows')
}

/**
 * 获取工作流信息
 * @param {string} workflowId - 工作流ID
 * @returns {Promise<Object>} 工作流信息
 */
export const getWorkflowInfo = async (workflowId) => {
  return await api.get(`/workflows/${workflowId}`)
}

/**
 * 健康检查
 * @returns {Promise<Object>} 健康状态
 */
export const healthCheck = async () => {
  return await api.get('/health')
}

export default api
