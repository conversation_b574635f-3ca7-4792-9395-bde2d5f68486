// 全局变量
let currentTaskId = null;
let queueCheckInterval = null;
let progressInterval = null;

// DOM 元素
const elements = {
    // 页面
    mainPage: document.getElementById('main-page'),
    waitingPage: document.getElementById('waiting-page'),
    resultPage: document.getElementById('result-page'),
    errorPage: document.getElementById('error-page'),
    
    // 输入相关
    promptInput: document.getElementById('prompt-input'),
    charCount: document.getElementById('char-count'),
    negativeInput: document.getElementById('negative-input'),
    negativeCharCount: document.getElementById('negative-char-count'),
    sizeSelect: document.getElementById('size-select'),
    generateBtn: document.getElementById('generate-btn'),
    
    // 队列信息
    queueCount: document.getElementById('queue-count'),
    
    // 等待页面
    progressFill: document.getElementById('progress-fill'),
    progressStatus: document.getElementById('progress-status'),
    positionNumber: document.getElementById('position-number'),
    estimatedTime: document.getElementById('estimated-time'),
    cancelBtn: document.getElementById('cancel-btn'),
    
    // 结果页面
    generatedImage: document.getElementById('generated-image'),
    usedPrompt: document.getElementById('used-prompt'),
    downloadBtn: document.getElementById('download-btn'),
    shareBtn: document.getElementById('share-btn'),
    generateAgainBtn: document.getElementById('generate-again-btn'),
    newPromptBtn: document.getElementById('new-prompt-btn'),
    
    // 错误页面
    errorMessage: document.getElementById('error-message'),
    retryBtn: document.getElementById('retry-btn'),
    backHomeBtn: document.getElementById('back-home-btn'),
    
    // Toast
    toast: document.getElementById('toast'),
    toastMessage: document.getElementById('toast-message')
};

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    bindEvents();
    updateQueueInfo();
});

// 初始化应用
function initializeApp() {
    // 检查输入框内容
    checkInputValidity();
    
    // 定期更新队列信息
    setInterval(updateQueueInfo, 5000);
}

// 绑定事件
function bindEvents() {
    // 输入框事件
    elements.promptInput.addEventListener('input', function() {
        updateCharCount();
        checkInputValidity();
    });

    elements.negativeInput.addEventListener('input', function() {
        updateNegativeCharCount();
    });
    
    // 生成按钮
    elements.generateBtn.addEventListener('click', handleGenerate);
    
    // 取消按钮
    elements.cancelBtn.addEventListener('click', handleCancel);
    
    // 结果页面按钮
    elements.downloadBtn.addEventListener('click', handleDownload);
    elements.shareBtn.addEventListener('click', handleShare);
    elements.generateAgainBtn.addEventListener('click', handleGenerateAgain);
    elements.newPromptBtn.addEventListener('click', handleNewPrompt);
    
    // 错误页面按钮
    elements.retryBtn.addEventListener('click', handleRetry);
    elements.backHomeBtn.addEventListener('click', handleBackHome);
}

// 更新字符计数
function updateCharCount() {
    const count = elements.promptInput.value.length;
    elements.charCount.textContent = count;

    if (count > 900) {
        elements.charCount.style.color = '#dc3545';
    } else if (count > 800) {
        elements.charCount.style.color = '#ffc107';
    } else {
        elements.charCount.style.color = '#666';
    }
}

// 更新反向提示词字符计数
function updateNegativeCharCount() {
    const count = elements.negativeInput.value.length;
    elements.negativeCharCount.textContent = count;

    if (count > 450) {
        elements.negativeCharCount.style.color = '#dc3545';
    } else if (count > 400) {
        elements.negativeCharCount.style.color = '#ffc107';
    } else {
        elements.negativeCharCount.style.color = '#666';
    }
}

// 检查输入有效性
function checkInputValidity() {
    const prompt = elements.promptInput.value.trim();
    const isValid = prompt.length >= 10 && prompt.length <= 1000;
    
    elements.generateBtn.disabled = !isValid;
    
    if (prompt.length > 0 && prompt.length < 10) {
        showToast('描述至少需要10个字符', 'warning');
    }
}

// 显示页面
function showPage(pageId) {
    // 隐藏所有页面
    document.querySelectorAll('.page').forEach(page => {
        page.classList.remove('active');
    });
    
    // 显示目标页面
    document.getElementById(pageId).classList.add('active');
}

// 显示Toast通知
function showToast(message, type = 'info') {
    elements.toastMessage.textContent = message;
    elements.toast.className = `toast ${type}`;
    elements.toast.classList.add('show');
    
    setTimeout(() => {
        elements.toast.classList.remove('show');
    }, 3000);
}

// 处理生成请求
async function handleGenerate() {
    const prompt = elements.promptInput.value.trim();
    const negativePrompt = elements.negativeInput.value.trim();
    const size = elements.sizeSelect.value;
    
    if (!prompt) {
        showToast('请输入图片描述', 'error');
        return;
    }
    
    try {
        // 显示等待页面
        showPage('waiting-page');
        
        // 重置进度
        resetProgress();
        
        // 发送生成请求
        const response = await fetch('/api/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                prompt: prompt,
                negativePrompt: negativePrompt,
                size: size
            })
        });
        
        if (!response.ok) {
            throw new Error('请求失败');
        }
        
        const data = await response.json();
        currentTaskId = data.taskId;
        
        // 开始检查任务状态
        startTaskMonitoring();
        
    } catch (error) {
        console.error('生成请求失败:', error);
        showErrorPage('请求发送失败，请检查网络连接');
    }
}

// 开始任务监控
function startTaskMonitoring() {
    elements.progressStatus.textContent = '已加入队列';
    
    queueCheckInterval = setInterval(async () => {
        try {
            const response = await fetch(`/api/status/${currentTaskId}`);
            const data = await response.json();
            
            updateTaskStatus(data);
            
            if (data.status === 'completed') {
                clearInterval(queueCheckInterval);
                showResult(data.result);
            } else if (data.status === 'failed') {
                clearInterval(queueCheckInterval);
                showErrorPage(data.error || '生成失败');
            }
            
        } catch (error) {
            console.error('状态检查失败:', error);
        }
    }, 2000);
}

// 更新任务状态
function updateTaskStatus(data) {
    const { status, position, estimatedTime, progress } = data;
    
    switch (status) {
        case 'queued':
            elements.progressStatus.textContent = '排队等待中';
            elements.positionNumber.textContent = position || 1;
            elements.estimatedTime.textContent = estimatedTime || '30-60秒';
            elements.progressFill.style.width = '10%';
            break;
            
        case 'processing':
            elements.progressStatus.textContent = '正在生成';
            elements.positionNumber.textContent = '0';
            elements.estimatedTime.textContent = '即将完成';
            elements.progressFill.style.width = `${Math.min(progress || 50, 90)}%`;
            break;
    }
}

// 重置进度
function resetProgress() {
    elements.progressFill.style.width = '0%';
    elements.progressStatus.textContent = '准备中';
    elements.positionNumber.textContent = '1';
    elements.estimatedTime.textContent = '30-60秒';
}

// 显示结果
function showResult(result) {
    elements.generatedImage.src = result.imageUrl;
    elements.usedPrompt.textContent = result.prompt;
    showPage('result-page');
    showToast('图片生成完成！', 'success');
}

// 显示错误页面
function showErrorPage(message) {
    elements.errorMessage.textContent = message;
    showPage('error-page');
}

// 更新队列信息
async function updateQueueInfo() {
    try {
        const response = await fetch('/api/queue-info');
        const data = await response.json();
        elements.queueCount.textContent = data.queueLength || 0;
    } catch (error) {
        console.error('获取队列信息失败:', error);
    }
}

// 事件处理函数
function handleCancel() {
    if (currentTaskId && queueCheckInterval) {
        clearInterval(queueCheckInterval);
        
        // 发送取消请求
        fetch(`/api/cancel/${currentTaskId}`, { method: 'POST' })
            .catch(error => console.error('取消请求失败:', error));
        
        currentTaskId = null;
        showPage('main-page');
        showToast('已取消生成', 'info');
    }
}

function handleDownload() {
    const imageUrl = elements.generatedImage.src;
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `ai-generated-${Date.now()}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    showToast('图片下载中...', 'success');
}

function handleShare() {
    if (navigator.share) {
        navigator.share({
            title: 'AI生成的图片',
            text: '看看我用AI生成的图片！',
            url: window.location.href
        });
    } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(window.location.href)
            .then(() => showToast('链接已复制到剪贴板', 'success'))
            .catch(() => showToast('分享失败', 'error'));
    }
}

function handleGenerateAgain() {
    showPage('main-page');
}

function handleNewPrompt() {
    elements.promptInput.value = '';
    updateCharCount();
    checkInputValidity();
    showPage('main-page');
}

function handleRetry() {
    handleGenerate();
}

function handleBackHome() {
    showPage('main-page');
}
