const express = require('express');
const cors = require('cors');
const axios = require('axios');
const fs = require('fs');
const fsPromises = require('fs').promises;
const path = require('path');
const WorkflowManager = require('./workflowManager');
const ComfyUIConfig = require('./comfyuiConfig');

const app = express();
const PORT = process.env.PORT || 5000;

console.log('启动后端API服务器...');

// 任务状态存储
const taskStatus = new Map();

// 工作流管理器和ComfyUI配置
const workflowManager = new WorkflowManager();
const comfyUIConfig = new ComfyUIConfig();

// 中间件配置
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3005'],
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 静态文件服务（用于生产环境）
const staticPath = process.env.NODE_ENV === 'production'
  ? path.join(__dirname, 'dist')
  : path.join(__dirname, '../frontend/dist');

console.log('静态文件路径:', staticPath);
if (fs.existsSync(staticPath)) {
  app.use(express.static(staticPath));
  console.log('✅ 静态文件服务已启用');
} else {
  console.log('⚠️ 静态文件目录不存在:', staticPath);
}

// 请求日志中间件
app.use('/api', (req, res, next) => {
  console.log(`📥 API请求: ${req.method} ${req.originalUrl}`);
  console.log(`📍 请求路径: ${req.path}`);
  console.log(`🔗 完整URL: ${req.protocol}://${req.get('host')}${req.originalUrl}`);
  next();
});

// API路由
app.get('/api/health', (req, res) => {
  console.log('✅ 收到健康检查请求');
  res.json({
    status: 'ok',
    timestamp: new Date(),
    server: 'AI Image Generator Backend',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  });
});

// 生成图片API
app.post('/api/generate', async (req, res) => {
  console.log('🎨 收到生成图片请求');
  console.log('📝 请求体:', JSON.stringify(req.body, null, 2));
  console.log('📋 请求头:', req.headers);

  try {
    const { prompt, negativePrompt, size, workflowId = 'reba' } = req.body;

    // 详细参数验证
    console.log('🔍 开始参数验证...');
    console.log('- prompt:', typeof prompt, prompt ? `"${prompt}"` : 'undefined');
    console.log('- negativePrompt:', typeof negativePrompt, negativePrompt ? `"${negativePrompt}"` : 'undefined');
    console.log('- size:', typeof size, size);
    console.log('- workflowId:', typeof workflowId, workflowId);

    // 检查必需参数
    if (!prompt) {
      console.error('❌ 参数验证失败: prompt为空');
      return res.status(400).json({
        success: false,
        error: '请输入图片描述',
        code: 'MISSING_PROMPT',
        details: 'prompt参数是必需的'
      });
    }

    if (typeof prompt !== 'string') {
      console.error('❌ 参数验证失败: prompt类型错误');
      return res.status(400).json({
        success: false,
        error: '图片描述必须是文本格式',
        code: 'INVALID_PROMPT_TYPE',
        details: `prompt应该是string类型，但收到的是${typeof prompt}`
      });
    }

    const trimmedPrompt = prompt.trim();
    if (trimmedPrompt.length < 3) {
      console.error('❌ 参数验证失败: prompt太短');
      return res.status(400).json({
        success: false,
        error: '图片描述至少需要3个字符',
        code: 'PROMPT_TOO_SHORT',
        details: `当前长度: ${trimmedPrompt.length}, 最少需要: 3`
      });
    }

    if (trimmedPrompt.length > 1000) {
      console.error('❌ 参数验证失败: prompt太长');
      return res.status(400).json({
        success: false,
        error: '图片描述不能超过1000个字符',
        code: 'PROMPT_TOO_LONG',
        details: `当前长度: ${trimmedPrompt.length}, 最大允许: 1000`
      });
    }

    // 验证工作流ID
    if (workflowId && typeof workflowId !== 'string') {
      console.error('❌ 参数验证失败: workflowId类型错误');
      return res.status(400).json({
        success: false,
        error: '工作流ID格式错误',
        code: 'INVALID_WORKFLOW_ID',
        details: `workflowId应该是string类型，但收到的是${typeof workflowId}`
      });
    }

    console.log('✅ 参数验证通过');
    
    console.log('开始处理真实ComfyUI生成请求...');
    console.log('- 工作流:', workflowId);
    console.log('- 正面提示词:', prompt);
    console.log('- 反向提示词:', negativePrompt || '(无)');
    console.log('- 图片尺寸:', size);
    
    // 生成任务ID
    const taskId = `task_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    
    // 初始化任务状态
    taskStatus.set(taskId, {
      status: 'queued',
      progress: 5,
      position: 1,
      estimatedTime: '60-120秒',
      prompt: prompt.trim(),
      negativePrompt: negativePrompt?.trim() || '',
      size: size || '720x480',
      workflowId: workflowId,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    console.log(`任务创建成功: ${taskId}`);
    
    // 异步处理ComfyUI请求
    processComfyUIRequest(taskId).catch(error => {
      console.error(`异步处理失败: ${taskId}`, error);
      const task = taskStatus.get(taskId);
      if (task) {
        task.status = 'failed';
        task.error = error.message;
        task.updatedAt = new Date();
      }
    });
    
    // 立即返回任务ID
    res.json({
      success: true,
      taskId: taskId,
      status: 'queued',
      position: 1,
      estimatedTime: '60-120秒',
      message: '任务已提交到ComfyUI队列'
    });
    
  } catch (error) {
    console.error('❌ 生成请求处理失败:', error);
    console.error('❌ 错误堆栈:', error.stack);
    res.status(500).json({
      success: false,
      error: '服务器内部错误',
      code: 'INTERNAL_ERROR',
      message: error.message,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// 获取任务状态
app.get('/api/status/:taskId', (req, res) => {
  const { taskId } = req.params;
  console.log('收到状态查询:', taskId);
  
  const task = taskStatus.get(taskId);
  
  if (!task) {
    return res.status(404).json({ 
      error: '任务不存在',
      code: 'TASK_NOT_FOUND'
    });
  }
  
  res.json({
    success: true,
    status: task.status,
    progress: task.progress || 0,
    position: task.position || 0,
    estimatedTime: task.estimatedTime || '计算中...',
    result: task.result,
    error: task.error,
    createdAt: task.createdAt,
    updatedAt: task.updatedAt
  });
});

// 取消任务
app.post('/api/cancel/:taskId', (req, res) => {
  const { taskId } = req.params;
  console.log('收到取消请求:', taskId);
  
  const task = taskStatus.get(taskId);
  
  if (!task) {
    return res.status(404).json({ 
      error: '任务不存在',
      code: 'TASK_NOT_FOUND'
    });
  }
  
  if (task.status === 'completed') {
    return res.json({
      success: false,
      message: '任务已完成，无法取消'
    });
  }
  
  task.status = 'cancelled';
  task.updatedAt = new Date();
  
  res.json({
    success: true,
    message: '任务已取消'
  });
});

// 获取队列信息
app.get('/api/queue-info', (req, res) => {
  console.log('收到队列信息请求');

  const allTasks = Array.from(taskStatus.values());
  const queuedTasks = allTasks.filter(task => task.status === 'queued');
  const processingTasks = allTasks.filter(task => task.status === 'processing');

  res.json({
    success: true,
    queueLength: queuedTasks.length,
    processingCount: processingTasks.length,
    totalTasks: allTasks.length,
    completedTasks: allTasks.filter(task => task.status === 'completed').length,
    failedTasks: allTasks.filter(task => task.status === 'failed').length
  });
});

// 获取可用工作流
app.get('/api/workflows', async (req, res) => {
  try {
    console.log('收到获取工作流列表请求');
    const workflows = await workflowManager.getAvailableWorkflows();
    res.json({
      success: true,
      workflows: workflows
    });
  } catch (error) {
    console.error('获取工作流列表失败:', error);
    res.status(500).json({
      error: '获取工作流列表失败',
      message: error.message
    });
  }
});

// ComfyUI配置管理API
app.get('/api/comfyui/config', (req, res) => {
  try {
    console.log('收到获取ComfyUI配置请求');
    const config = comfyUIConfig.getStatus();

    res.json({
      success: true,
      config: config
    });
  } catch (error) {
    console.error('获取ComfyUI配置失败:', error);
    res.status(500).json({
      success: false,
      error: '获取ComfyUI配置失败',
      message: error.message
    });
  }
});

app.post('/api/comfyui/config', async (req, res) => {
  try {
    console.log('收到更新ComfyUI配置请求:', req.body);
    const { baseURL, timeout } = req.body;

    if (!baseURL) {
      return res.status(400).json({
        success: false,
        error: 'baseURL是必需的'
      });
    }

    // 验证新配置
    const isValid = await comfyUIConfig.validateConfig(baseURL);
    if (!isValid) {
      return res.status(400).json({
        success: false,
        error: 'ComfyUI服务器连接失败，请检查地址是否正确'
      });
    }

    // 更新配置
    comfyUIConfig.updateConfig({ baseURL, timeout });

    res.json({
      success: true,
      message: 'ComfyUI配置已更新',
      config: comfyUIConfig.getStatus()
    });
  } catch (error) {
    console.error('更新ComfyUI配置失败:', error);
    res.status(500).json({
      success: false,
      error: '更新ComfyUI配置失败',
      message: error.message
    });
  }
});

app.post('/api/comfyui/test', async (req, res) => {
  try {
    console.log('收到测试ComfyUI连接请求:', req.body);
    const { baseURL } = req.body;

    if (!baseURL) {
      return res.status(400).json({
        success: false,
        error: 'baseURL是必需的'
      });
    }

    const isValid = await comfyUIConfig.validateConfig(baseURL);

    res.json({
      success: true,
      connected: isValid,
      message: isValid ? 'ComfyUI连接成功' : 'ComfyUI连接失败'
    });
  } catch (error) {
    console.error('测试ComfyUI连接失败:', error);
    res.status(500).json({
      success: false,
      error: '测试ComfyUI连接失败',
      message: error.message
    });
  }
});

app.post('/api/comfyui/auto-detect', async (req, res) => {
  try {
    console.log('收到自动检测ComfyUI请求');
    const { candidates } = req.body || {};

    const detectedUrl = await comfyUIConfig.autoDetect(candidates);

    if (detectedUrl) {
      // 自动更新配置
      comfyUIConfig.updateConfig({ baseURL: detectedUrl });

      res.json({
        success: true,
        detected: true,
        baseURL: detectedUrl,
        message: '自动检测成功并已更新配置'
      });
    } else {
      res.json({
        success: true,
        detected: false,
        message: '未检测到可用的ComfyUI服务器'
      });
    }
  } catch (error) {
    console.error('自动检测ComfyUI失败:', error);
    res.status(500).json({
      success: false,
      error: '自动检测ComfyUI失败',
      message: error.message
    });
  }
});

// 获取工作流信息
app.get('/api/workflows/:workflowId', async (req, res) => {
  try {
    const { workflowId } = req.params;
    console.log('收到获取工作流信息请求:', workflowId);

    const workflowInfo = await workflowManager.getWorkflowInfo(workflowId);
    res.json({
      success: true,
      workflow: workflowInfo
    });
  } catch (error) {
    console.error('获取工作流信息失败:', error);
    res.status(404).json({
      error: '工作流不存在',
      message: error.message
    });
  }
});

// API 404错误处理
app.use('/api/*', (req, res) => {
  console.error(`❌ API路由不存在: ${req.method} ${req.originalUrl}`);
  res.status(404).json({
    success: false,
    error: 'API接口不存在',
    message: `路由 ${req.method} ${req.originalUrl} 不存在`,
    availableRoutes: [
      'GET /api/health',
      'GET /api/workflows',
      'GET /api/queue-info',
      'POST /api/generate',
      'GET /api/status/:taskId',
      'POST /api/cancel/:taskId',
      'GET /api/comfyui/config',
      'POST /api/comfyui/config',
      'POST /api/comfyui/test',
      'POST /api/comfyui/auto-detect'
    ]
  });
});

// 处理Vue路由（SPA支持）
app.get('*', (req, res) => {
  const indexPath = process.env.NODE_ENV === 'production'
    ? path.join(__dirname, 'dist/index.html')
    : path.join(__dirname, '../frontend/dist/index.html');

  console.log('请求SPA路由，返回:', indexPath);

  if (fs.existsSync(indexPath)) {
    res.sendFile(indexPath);
  } else {
    console.error('index.html文件不存在:', indexPath);
    res.status(404).send('Frontend not found. Please build the frontend first.');
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`后端API服务器运行在 http://localhost:${PORT}`);
  console.log('支持的功能:');
  console.log('- ComfyUI图片生成');
  console.log('- 任务队列管理');
  console.log('- 实时状态监控');
  console.log('- Vue前端支持');
});

// ComfyUI处理函数
async function processComfyUIRequest(taskId) {
  console.log(`🚀 开始处理ComfyUI任务: ${taskId}`);
  
  const task = taskStatus.get(taskId);
  if (!task) {
    throw new Error('任务不存在');
  }
  
  try {
    // 1. 加载工作流文件
    console.log('📖 加载工作流文件...');
    const workflow = await workflowManager.loadWorkflow(task.workflowId);

    // 2. 更新工作流参数
    console.log('⚙️ 更新工作流参数...');
    workflowManager.updateWorkflowParams(workflow, {
      prompt: task.prompt,
      negativePrompt: task.negativePrompt,
      size: task.size,
      workflowId: task.workflowId
    });
    
    // 3. 更新任务状态
    task.status = 'processing';
    task.progress = 20;
    task.estimatedTime = '30-90秒';
    task.updatedAt = new Date();
    
    // 4. 发送到ComfyUI
    console.log('🌐 发送请求到ComfyUI...');
    const promptUrl = comfyUIConfig.getPromptUrl();
    console.log('ComfyUI URL:', promptUrl);

    const response = await axios.post(
      promptUrl,
      { prompt: workflow },
      {
        timeout: comfyUIConfig.getConfig().timeout,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      }
    );
    
    console.log('✅ ComfyUI响应成功:', response.status);
    const promptId = response.data.prompt_id;
    console.log('📋 获得prompt_id:', promptId);
    
    task.promptId = promptId;
    task.progress = 40;
    task.updatedAt = new Date();
    
    // 5. 等待ComfyUI完成
    console.log('⏳ 等待ComfyUI处理完成...');
    const imageResult = await waitForComfyUIResult(promptId, task);
    
    // 6. 更新任务状态为完成
    task.status = 'completed';
    task.progress = 100;
    task.result = {
      imageUrl: imageResult.imageUrl,
      prompt: task.prompt,
      negativePrompt: task.negativePrompt,
      size: task.size,
      filename: imageResult.filename,
      promptId: promptId,
      generatedAt: new Date()
    };
    task.updatedAt = new Date();
    
    console.log('🎉 任务完成:', taskId);
    console.log('🖼️ 图片URL:', imageResult.imageUrl);
    
  } catch (error) {
    console.error('❌ ComfyUI处理失败:', taskId, error.message);
    
    task.status = 'failed';
    task.progress = 0;
    task.error = `处理失败: ${error.message}`;
    task.updatedAt = new Date();
    
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}



// 等待ComfyUI结果
async function waitForComfyUIResult(promptId, task) {
  const maxWaitTime = 180000; // 3分钟
  const checkInterval = 5000; // 5秒检查一次
  const startTime = Date.now();
  
  console.log(`🔍 开始监控ComfyUI进度: ${promptId}`);
  
  while (Date.now() - startTime < maxWaitTime) {
    if (task.status === 'cancelled') {
      throw new Error('任务已取消');
    }
    
    try {
      // 更新进度
      const elapsed = Date.now() - startTime;
      const progressPercent = Math.min(95, 40 + (elapsed / maxWaitTime) * 55);
      task.progress = Math.round(progressPercent);
      task.updatedAt = new Date();
      
      // 检查队列状态
      const queueUrl = comfyUIConfig.getQueueUrl();
      const queueResponse = await axios.get(queueUrl, { timeout: 10000 });
      
      const queueData = queueResponse.data;
      const isInQueue = queueData.queue_running?.some(item => item[1] === promptId) ||
                       queueData.queue_pending?.some(item => item[1] === promptId);
      
      if (!isInQueue) {
        console.log('✅ 任务不在队列中，检查历史记录...');
        
        // 检查历史记录
        const historyUrl = comfyUIConfig.getHistoryUrl(promptId);
        const historyResponse = await axios.get(historyUrl, { timeout: 10000 });
        
        const historyData = historyResponse.data;
        
        if (historyData[promptId]) {
          const outputs = historyData[promptId].outputs;
          console.log('🔍 找到输出:', Object.keys(outputs));
          
          // 查找图片
          for (const nodeId in outputs) {
            const nodeOutput = outputs[nodeId];
            if (nodeOutput.images && nodeOutput.images.length > 0) {
              const imageInfo = nodeOutput.images[0];
              console.log('🖼️ 找到图片:', imageInfo);
              
              const imageUrl = comfyUIConfig.getViewUrl(
                imageInfo.filename,
                imageInfo.subfolder || '',
                imageInfo.type || 'output'
              );
              
              return {
                imageUrl: imageUrl,
                filename: imageInfo.filename
              };
            }
          }
        }
      } else {
        console.log('⏳ 任务还在队列中处理...');
      }
      
      await new Promise(resolve => setTimeout(resolve, checkInterval));
      
    } catch (error) {
      console.error('监控失败:', error.message);
      if (error.response?.status !== 404) {
        await new Promise(resolve => setTimeout(resolve, checkInterval));
      }
    }
  }
  
  throw new Error('ComfyUI处理超时');
}

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('未捕获异常:', error);
});

process.on('unhandledRejection', (reason) => {
  console.error('未处理的Promise拒绝:', reason);
});
