const express = require('express');
const cors = require('cors');
const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
const WorkflowManager = require('./workflowManager');

const app = express();
const PORT = process.env.PORT || 5000;

console.log('启动后端API服务器...');

// 任务状态存储
const taskStatus = new Map();

// 工作流管理器
const workflowManager = new WorkflowManager();

// 中间件配置
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3005'],
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 静态文件服务（用于生产环境）
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, 'dist')));
} else {
  app.use(express.static(path.join(__dirname, '../frontend/dist')));
}

// API路由
app.get('/api/health', (req, res) => {
  console.log('收到健康检查请求');
  res.json({ 
    status: 'ok', 
    timestamp: new Date(),
    server: 'AI Image Generator Backend',
    version: '1.0.0'
  });
});

// 生成图片API
app.post('/api/generate', async (req, res) => {
  console.log('收到生成图片请求:', req.body);

  try {
    const { prompt, negativePrompt, size, workflowId = 'reba' } = req.body;
    
    // 参数验证
    if (!prompt || prompt.trim().length < 10) {
      return res.status(400).json({ 
        error: '描述至少需要10个字符',
        code: 'INVALID_PROMPT'
      });
    }
    
    if (prompt.trim().length > 1000) {
      return res.status(400).json({ 
        error: '描述不能超过1000个字符',
        code: 'PROMPT_TOO_LONG'
      });
    }
    
    console.log('开始处理真实ComfyUI生成请求...');
    console.log('- 工作流:', workflowId);
    console.log('- 正面提示词:', prompt);
    console.log('- 反向提示词:', negativePrompt || '(无)');
    console.log('- 图片尺寸:', size);
    
    // 生成任务ID
    const taskId = `task_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    
    // 初始化任务状态
    taskStatus.set(taskId, {
      status: 'queued',
      progress: 5,
      position: 1,
      estimatedTime: '60-120秒',
      prompt: prompt.trim(),
      negativePrompt: negativePrompt?.trim() || '',
      size: size || '720x480',
      workflowId: workflowId,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    console.log(`任务创建成功: ${taskId}`);
    
    // 异步处理ComfyUI请求
    processComfyUIRequest(taskId).catch(error => {
      console.error(`异步处理失败: ${taskId}`, error);
      const task = taskStatus.get(taskId);
      if (task) {
        task.status = 'failed';
        task.error = error.message;
        task.updatedAt = new Date();
      }
    });
    
    // 立即返回任务ID
    res.json({
      success: true,
      taskId: taskId,
      status: 'queued',
      position: 1,
      estimatedTime: '60-120秒',
      message: '任务已提交到ComfyUI队列'
    });
    
  } catch (error) {
    console.error('生成请求处理失败:', error);
    res.status(500).json({
      error: '服务器内部错误',
      code: 'INTERNAL_ERROR',
      message: error.message
    });
  }
});

// 获取任务状态
app.get('/api/status/:taskId', (req, res) => {
  const { taskId } = req.params;
  console.log('收到状态查询:', taskId);
  
  const task = taskStatus.get(taskId);
  
  if (!task) {
    return res.status(404).json({ 
      error: '任务不存在',
      code: 'TASK_NOT_FOUND'
    });
  }
  
  res.json({
    success: true,
    status: task.status,
    progress: task.progress || 0,
    position: task.position || 0,
    estimatedTime: task.estimatedTime || '计算中...',
    result: task.result,
    error: task.error,
    createdAt: task.createdAt,
    updatedAt: task.updatedAt
  });
});

// 取消任务
app.post('/api/cancel/:taskId', (req, res) => {
  const { taskId } = req.params;
  console.log('收到取消请求:', taskId);
  
  const task = taskStatus.get(taskId);
  
  if (!task) {
    return res.status(404).json({ 
      error: '任务不存在',
      code: 'TASK_NOT_FOUND'
    });
  }
  
  if (task.status === 'completed') {
    return res.json({
      success: false,
      message: '任务已完成，无法取消'
    });
  }
  
  task.status = 'cancelled';
  task.updatedAt = new Date();
  
  res.json({
    success: true,
    message: '任务已取消'
  });
});

// 获取队列信息
app.get('/api/queue-info', (req, res) => {
  console.log('收到队列信息请求');

  const allTasks = Array.from(taskStatus.values());
  const queuedTasks = allTasks.filter(task => task.status === 'queued');
  const processingTasks = allTasks.filter(task => task.status === 'processing');

  res.json({
    success: true,
    queueLength: queuedTasks.length,
    processingCount: processingTasks.length,
    totalTasks: allTasks.length,
    completedTasks: allTasks.filter(task => task.status === 'completed').length,
    failedTasks: allTasks.filter(task => task.status === 'failed').length
  });
});

// 获取可用工作流
app.get('/api/workflows', async (req, res) => {
  try {
    console.log('收到获取工作流列表请求');
    const workflows = await workflowManager.getAvailableWorkflows();
    res.json({
      success: true,
      workflows: workflows
    });
  } catch (error) {
    console.error('获取工作流列表失败:', error);
    res.status(500).json({
      error: '获取工作流列表失败',
      message: error.message
    });
  }
});

// 获取工作流信息
app.get('/api/workflows/:workflowId', async (req, res) => {
  try {
    const { workflowId } = req.params;
    console.log('收到获取工作流信息请求:', workflowId);

    const workflowInfo = await workflowManager.getWorkflowInfo(workflowId);
    res.json({
      success: true,
      workflow: workflowInfo
    });
  } catch (error) {
    console.error('获取工作流信息失败:', error);
    res.status(404).json({
      error: '工作流不存在',
      message: error.message
    });
  }
});

// 处理Vue路由（SPA支持）
app.get('*', (req, res) => {
  if (process.env.NODE_ENV === 'production') {
    res.sendFile(path.join(__dirname, 'dist/index.html'));
  } else {
    res.sendFile(path.join(__dirname, '../frontend/dist/index.html'));
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`后端API服务器运行在 http://localhost:${PORT}`);
  console.log('支持的功能:');
  console.log('- ComfyUI图片生成');
  console.log('- 任务队列管理');
  console.log('- 实时状态监控');
  console.log('- Vue前端支持');
});

// ComfyUI处理函数
async function processComfyUIRequest(taskId) {
  console.log(`🚀 开始处理ComfyUI任务: ${taskId}`);
  
  const task = taskStatus.get(taskId);
  if (!task) {
    throw new Error('任务不存在');
  }
  
  try {
    // 1. 加载工作流文件
    console.log('📖 加载工作流文件...');
    const workflow = await workflowManager.loadWorkflow(task.workflowId);

    // 2. 更新工作流参数
    console.log('⚙️ 更新工作流参数...');
    workflowManager.updateWorkflowParams(workflow, {
      prompt: task.prompt,
      negativePrompt: task.negativePrompt,
      size: task.size,
      workflowId: task.workflowId
    });
    
    // 3. 更新任务状态
    task.status = 'processing';
    task.progress = 20;
    task.estimatedTime = '30-90秒';
    task.updatedAt = new Date();
    
    // 4. 发送到ComfyUI
    console.log('🌐 发送请求到ComfyUI...');
    const response = await axios.post(
      'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/prompt',
      { prompt: workflow },
      {
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      }
    );
    
    console.log('✅ ComfyUI响应成功:', response.status);
    const promptId = response.data.prompt_id;
    console.log('📋 获得prompt_id:', promptId);
    
    task.promptId = promptId;
    task.progress = 40;
    task.updatedAt = new Date();
    
    // 5. 等待ComfyUI完成
    console.log('⏳ 等待ComfyUI处理完成...');
    const imageResult = await waitForComfyUIResult(promptId, task);
    
    // 6. 更新任务状态为完成
    task.status = 'completed';
    task.progress = 100;
    task.result = {
      imageUrl: imageResult.imageUrl,
      prompt: task.prompt,
      negativePrompt: task.negativePrompt,
      size: task.size,
      filename: imageResult.filename,
      promptId: promptId,
      generatedAt: new Date()
    };
    task.updatedAt = new Date();
    
    console.log('🎉 任务完成:', taskId);
    console.log('🖼️ 图片URL:', imageResult.imageUrl);
    
  } catch (error) {
    console.error('❌ ComfyUI处理失败:', taskId, error.message);
    
    task.status = 'failed';
    task.progress = 0;
    task.error = `处理失败: ${error.message}`;
    task.updatedAt = new Date();
    
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}



// 等待ComfyUI结果
async function waitForComfyUIResult(promptId, task) {
  const maxWaitTime = 180000; // 3分钟
  const checkInterval = 5000; // 5秒检查一次
  const startTime = Date.now();
  
  console.log(`🔍 开始监控ComfyUI进度: ${promptId}`);
  
  while (Date.now() - startTime < maxWaitTime) {
    if (task.status === 'cancelled') {
      throw new Error('任务已取消');
    }
    
    try {
      // 更新进度
      const elapsed = Date.now() - startTime;
      const progressPercent = Math.min(95, 40 + (elapsed / maxWaitTime) * 55);
      task.progress = Math.round(progressPercent);
      task.updatedAt = new Date();
      
      // 检查队列状态
      const queueResponse = await axios.get(
        'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/queue',
        { timeout: 10000 }
      );
      
      const queueData = queueResponse.data;
      const isInQueue = queueData.queue_running?.some(item => item[1] === promptId) ||
                       queueData.queue_pending?.some(item => item[1] === promptId);
      
      if (!isInQueue) {
        console.log('✅ 任务不在队列中，检查历史记录...');
        
        // 检查历史记录
        const historyResponse = await axios.get(
          `https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/history/${promptId}`,
          { timeout: 10000 }
        );
        
        const historyData = historyResponse.data;
        
        if (historyData[promptId]) {
          const outputs = historyData[promptId].outputs;
          console.log('🔍 找到输出:', Object.keys(outputs));
          
          // 查找图片
          for (const nodeId in outputs) {
            const nodeOutput = outputs[nodeId];
            if (nodeOutput.images && nodeOutput.images.length > 0) {
              const imageInfo = nodeOutput.images[0];
              console.log('🖼️ 找到图片:', imageInfo);
              
              const imageUrl = `https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/view?filename=${imageInfo.filename}&subfolder=${imageInfo.subfolder || ''}&type=${imageInfo.type || 'output'}`;
              
              return {
                imageUrl: imageUrl,
                filename: imageInfo.filename
              };
            }
          }
        }
      } else {
        console.log('⏳ 任务还在队列中处理...');
      }
      
      await new Promise(resolve => setTimeout(resolve, checkInterval));
      
    } catch (error) {
      console.error('监控失败:', error.message);
      if (error.response?.status !== 404) {
        await new Promise(resolve => setTimeout(resolve, checkInterval));
      }
    }
  }
  
  throw new Error('ComfyUI处理超时');
}

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('未捕获异常:', error);
});

process.on('unhandledRejection', (reason) => {
  console.error('未处理的Promise拒绝:', reason);
});
