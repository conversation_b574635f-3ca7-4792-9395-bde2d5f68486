const axios = require('axios');

async function testRealComfyUI() {
    const baseURL = 'http://localhost:3005';
    
    console.log('🚀 测试真实ComfyUI工作流触发...\n');
    
    try {
        // 1. 健康检查
        console.log('1️⃣ 健康检查...');
        const healthResponse = await axios.get(`${baseURL}/health`);
        console.log('✅ 健康检查成功:', healthResponse.data);
        
        // 2. 发送真实的生成请求
        console.log('\n2️⃣ 发送真实ComfyUI生成请求...');
        const generateResponse = await axios.post(`${baseURL}/api/generate`, {
            prompt: 'a beautiful sunset over mountains, peaceful landscape, high quality, detailed',
            negativePrompt: 'blurry, low quality, distorted, ugly, bad anatomy',
            size: '720x480'
        });
        console.log('✅ 生成请求成功:', generateResponse.data);
        
        const taskId = generateResponse.data.taskId;
        console.log(`📋 任务ID: ${taskId}`);
        
        // 3. 监控真实处理进度
        console.log('\n3️⃣ 监控真实ComfyUI处理进度...');
        let attempts = 0;
        const maxAttempts = 50; // 最多检查50次（约4分钟）
        
        while (attempts < maxAttempts) {
            attempts++;
            
            try {
                const statusResponse = await axios.get(`${baseURL}/api/status/${taskId}`);
                const status = statusResponse.data;
                
                console.log(`📊 第${attempts}次检查:`);
                console.log(`   状态: ${status.status}`);
                console.log(`   进度: ${status.progress}%`);
                console.log(`   预计时间: ${status.estimatedTime}`);
                
                if (status.status === 'completed') {
                    console.log('\n🎉 真实ComfyUI任务完成!');
                    console.log('📸 生成结果:');
                    console.log(`   图片URL: ${status.result.imageUrl}`);
                    console.log(`   文件名: ${status.result.filename}`);
                    console.log(`   正面提示词: ${status.result.prompt}`);
                    console.log(`   反向提示词: ${status.result.negativePrompt}`);
                    console.log(`   图片尺寸: ${status.result.size}`);
                    console.log(`   ComfyUI ID: ${status.result.promptId}`);
                    console.log(`   生成时间: ${status.result.generatedAt}`);
                    
                    // 验证是否是新生成的图片
                    console.log('\n4️⃣ 验证图片是否为新生成...');
                    const filename = status.result.filename;
                    if (filename && filename !== 'ComfyUI_00001_.png') {
                        console.log('✅ 确认是新生成的图片文件:', filename);
                    } else {
                        console.log('⚠️ 可能不是新生成的图片:', filename);
                    }
                    
                    // 测试图片URL访问
                    try {
                        const imageResponse = await axios.head(status.result.imageUrl, { timeout: 15000 });
                        console.log('✅ 图片URL可访问，状态码:', imageResponse.status);
                        console.log('✅ 图片类型:', imageResponse.headers['content-type']);
                        
                        if (imageResponse.headers['content-length']) {
                            console.log('✅ 图片大小:', imageResponse.headers['content-length'], 'bytes');
                        }
                        
                    } catch (imageError) {
                        console.log('⚠️ 图片URL访问失败:', imageError.message);
                    }
                    
                    break;
                } else if (status.status === 'failed') {
                    console.log('\n❌ 任务失败:', status.error);
                    break;
                } else {
                    console.log(`   ⏳ 继续等待...`);
                    await new Promise(resolve => setTimeout(resolve, 5000)); // 等待5秒
                }
                
            } catch (statusError) {
                console.log(`⚠️ 状态查询失败: ${statusError.message}`);
                break;
            }
        }
        
        if (attempts >= maxAttempts) {
            console.log('\n⏰ 达到最大检查次数，停止监控');
        }
        
        console.log('\n🏁 真实ComfyUI测试完成!');
        
    } catch (error) {
        console.error('\n❌ 测试失败:');
        console.error('错误信息:', error.message);
        
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
        
        throw error;
    }
}

// 运行测试
if (require.main === module) {
    testRealComfyUI()
        .then(() => {
            console.log('\n✨ 真实ComfyUI测试完成!');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n💥 测试失败');
            process.exit(1);
        });
}

module.exports = testRealComfyUI;
