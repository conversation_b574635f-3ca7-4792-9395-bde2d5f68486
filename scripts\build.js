#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始构建AI图片生成器...\n');

try {
  // 1. 清理之前的构建
  console.log('1️⃣ 清理构建目录...');

  // 安全清理函数
  function safeRemove(dirPath) {
    if (fs.existsSync(dirPath)) {
      try {
        fs.rmSync(dirPath, { recursive: true, force: true });
        console.log(`✅ 清理 ${dirPath}`);
      } catch (error) {
        console.log(`⚠️ 清理 ${dirPath} 失败: ${error.message}`);
        // 尝试重命名然后删除
        try {
          const tempPath = dirPath + '_temp_' + Date.now();
          fs.renameSync(dirPath, tempPath);
          setTimeout(() => {
            try {
              fs.rmSync(tempPath, { recursive: true, force: true });
            } catch (e) {
              console.log(`⚠️ 延迟清理失败: ${e.message}`);
            }
          }, 1000);
        } catch (renameError) {
          console.log(`⚠️ 重命名清理失败: ${renameError.message}`);
        }
      }
    }
  }

  safeRemove('dist');
  safeRemove('backend/dist');
  safeRemove('frontend/dist');

  console.log('✅ 清理完成\n');

  // 2. 安装依赖
  console.log('2️⃣ 安装依赖...');
  console.log('安装前端依赖...');
  execSync('npm install', { cwd: 'frontend', stdio: 'inherit' });
  console.log('安装后端依赖...');
  execSync('npm install --only=production', { cwd: 'backend', stdio: 'inherit' });
  console.log('✅ 依赖安装完成\n');

  // 3. 构建前端
  console.log('3️⃣ 构建前端应用...');
  execSync('npm run build', { cwd: 'frontend', stdio: 'inherit' });
  console.log('✅ 前端构建完成\n');

  // 4. 验证前端构建结果
  console.log('4️⃣ 验证前端构建结果...');
  const frontendDist = path.join('frontend', 'dist');
  const frontendIndex = path.join(frontendDist, 'index.html');

  if (!fs.existsSync(frontendDist)) {
    throw new Error('前端构建失败，dist目录不存在');
  }

  if (!fs.existsSync(frontendIndex)) {
    throw new Error('前端构建失败，index.html文件不存在');
  }

  console.log('✅ 前端构建验证通过\n');

  // 5. 复制前端构建结果到后端
  console.log('5️⃣ 复制前端构建结果到后端...');
  const backendDist = path.join('backend', 'dist');

  // 确保后端dist目录存在
  if (!fs.existsSync(path.dirname(backendDist))) {
    fs.mkdirSync(path.dirname(backendDist), { recursive: true });
  }

  fs.cpSync(frontendDist, backendDist, { recursive: true });
  console.log('✅ 前端文件复制到后端完成\n');

  // 6. 创建生产环境目录结构
  console.log('6️⃣ 创建生产环境目录...');
  if (!fs.existsSync('dist')) {
    fs.mkdirSync('dist');
  }
  
  // 复制必要文件到dist目录
  const filesToCopy = [
    { src: 'backend', dest: 'dist/backend' },
    { src: 'workflow', dest: 'dist/workflow' },
    { src: 'package.json', dest: 'dist/package.json' },
    { src: 'ecosystem.config.js', dest: 'dist/ecosystem.config.js' },
    { src: '.env.example', dest: 'dist/.env.example' }
  ];

  filesToCopy.forEach(({ src, dest }) => {
    if (fs.existsSync(src)) {
      if (fs.statSync(src).isDirectory()) {
        fs.cpSync(src, dest, { recursive: true });
      } else {
        fs.copyFileSync(src, dest);
      }
      console.log(`✅ 复制 ${src} -> ${dest}`);
    }
  });

  // 7. 创建启动脚本
  console.log('\n7️⃣ 创建启动脚本...');
  
  // Linux/Mac 启动脚本
  const startScript = `#!/bin/bash
echo "启动AI图片生成器..."

# 检查Node.js版本
node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$node_version" -lt "14" ]; then
    echo "错误: 需要Node.js 14或更高版本"
    exit 1
fi

# 设置环境变量
export NODE_ENV=production
export PORT=\${PORT:-5000}

# 创建日志目录
mkdir -p logs

# 启动应用
if command -v pm2 &> /dev/null; then
    echo "使用PM2启动..."
    pm2 start ecosystem.config.js --env production
else
    echo "使用Node.js直接启动..."
    node backend/server.js
fi
`;

  fs.writeFileSync('dist/start.sh', startScript);
  fs.chmodSync('dist/start.sh', '755');

  // Windows 启动脚本
  const startBat = `@echo off
echo 启动AI图片生成器...

REM 设置环境变量
set NODE_ENV=production
if "%PORT%"=="" set PORT=5000

REM 创建日志目录
if not exist logs mkdir logs

REM 启动应用
where pm2 >nul 2>nul
if %errorlevel% == 0 (
    echo 使用PM2启动...
    pm2 start ecosystem.config.js --env production
) else (
    echo 使用Node.js直接启动...
    node backend/server.js
)
`;

  fs.writeFileSync('dist/start.bat', startBat);

  console.log('✅ 启动脚本创建完成\n');

  // 7. 创建部署说明
  const deploymentGuide = `# AI图片生成器 - 部署指南

## 系统要求
- Node.js >= 14.0.0
- npm >= 6.0.0
- PM2 (推荐，用于生产环境进程管理)

## 部署步骤

### 1. 上传文件
将整个 dist 目录上传到服务器

### 2. 安装PM2 (推荐)
\`\`\`bash
npm install -g pm2
\`\`\`

### 3. 配置环境变量
复制 .env.example 为 .env 并修改配置：
\`\`\`bash
cp .env.example .env
nano .env
\`\`\`

### 4. 启动应用

#### Linux/Mac:
\`\`\`bash
chmod +x start.sh
./start.sh
\`\`\`

#### Windows:
\`\`\`cmd
start.bat
\`\`\`

#### 手动启动:
\`\`\`bash
# 使用PM2
pm2 start ecosystem.config.js --env production

# 或直接使用Node.js
NODE_ENV=production PORT=5000 node backend/server.js
\`\`\`

### 5. 验证部署
访问 http://your-server:5000 检查应用是否正常运行

### 6. 配置反向代理 (可选)
使用Nginx或Apache配置反向代理，将80/443端口转发到5000端口

## PM2 管理命令
\`\`\`bash
pm2 list                    # 查看进程列表
pm2 logs ai-image-generator # 查看日志
pm2 restart ai-image-generator # 重启应用
pm2 stop ai-image-generator    # 停止应用
pm2 delete ai-image-generator  # 删除应用
\`\`\`

## 目录结构
\`\`\`
dist/
├── backend/           # 后端代码和依赖
├── workflow/          # ComfyUI工作流配置
├── logs/             # 日志文件 (运行时创建)
├── package.json      # 项目配置
├── ecosystem.config.js # PM2配置
├── .env.example      # 环境变量示例
├── start.sh          # Linux/Mac启动脚本
└── start.bat         # Windows启动脚本
\`\`\`
`;

  fs.writeFileSync('dist/DEPLOYMENT.md', deploymentGuide);

  console.log('🎉 构建完成！');
  console.log('\n📦 构建结果:');
  console.log('- 生产环境文件: ./dist/');
  console.log('- 部署指南: ./dist/DEPLOYMENT.md');
  console.log('- 启动脚本: ./dist/start.sh (Linux/Mac) 或 ./dist/start.bat (Windows)');
  console.log('\n🚀 部署步骤:');
  console.log('1. 将 dist 目录上传到服务器');
  console.log('2. 配置环境变量 (.env)');
  console.log('3. 运行启动脚本');

} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
}
