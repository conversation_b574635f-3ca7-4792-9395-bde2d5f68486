console.log('开始启动调试服务器...');

try {
    const express = require('express');
    console.log('Express 加载成功');
    
    const cors = require('cors');
    console.log('CORS 加载成功');
    
    const axios = require('axios');
    console.log('Axios 加载成功');
    
    const fs = require('fs').promises;
    console.log('FS 加载成功');
    
    const app = express();
    const PORT = process.env.PORT || 3001;
    
    console.log('创建 Express 应用成功');
    
    // 中间件
    app.use(cors());
    app.use(express.json());
    app.use(express.static('.'));
    
    console.log('中间件配置完成');
    
    // 简单的健康检查
    app.get('/health', (req, res) => {
        console.log('收到健康检查请求');
        res.json({ status: 'ok', timestamp: new Date() });
    });
    
    // 测试路由
    app.get('/test', (req, res) => {
        console.log('收到测试请求');
        res.json({ message: 'Server is working!' });
    });
    
    // 生成图片API
    app.post('/api/generate', async (req, res) => {
        console.log('收到生成图片请求:', req.body);
        
        try {
            const { prompt, style, size } = req.body;
            
            if (!prompt || prompt.trim().length < 10) {
                console.log('提示词太短');
                return res.status(400).json({ 
                    error: '描述至少需要10个字符' 
                });
            }
            
            console.log('开始读取工作流文件...');
            
            // 检查文件是否存在
            try {
                await fs.access('./reba.json');
                console.log('reba.json 文件存在');
            } catch (error) {
                console.error('reba.json 文件不存在:', error);
                return res.status(500).json({ error: 'reba.json 文件不存在' });
            }
            
            // 读取工作流文件
            const workflowData = await fs.readFile('./reba.json', 'utf8');
            const workflow = JSON.parse(workflowData);
            console.log('工作流文件读取成功');
            
            // 更新提示词
            if (workflow['27'] && workflow['27'].inputs) {
                workflow['27'].inputs.text = `best quality, masterpiece, ${prompt}`;
                console.log('更新正面提示词成功');
            }
            
            // 更新负面提示词
            const negativePrompt = 'worst quality, low quality, blurry, bad anatomy';
            if (workflow['1'] && workflow['1'].inputs) {
                workflow['1'].inputs.text = negativePrompt;
                console.log('更新负面提示词(节点1)成功');
            }
            if (workflow['20'] && workflow['20'].inputs) {
                workflow['20'].inputs.text = negativePrompt;
                console.log('更新负面提示词(节点20)成功');
            }
            
            // 更新图片尺寸
            if (size && workflow['25'] && workflow['25'].inputs) {
                const [width, height] = size.split('x').map(Number);
                workflow['25'].inputs.width = width;
                workflow['25'].inputs.height = height;
                console.log(`更新图片尺寸: ${width}x${height}`);
            }
            
            // 生成随机种子
            if (workflow['26'] && workflow['26'].inputs) {
                workflow['26'].inputs.seed = Math.floor(Math.random() * 1000000000);
                console.log('更新随机种子成功');
            }
            
            console.log('准备发送请求到ComfyUI...');
            console.log('ComfyUI URL:', 'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/prompt');
            
            // 发送到ComfyUI
            const response = await axios.post(
                'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/prompt',
                workflow,
                {
                    timeout: 60000,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                }
            );
            
            console.log('ComfyUI响应成功:', response.status);
            console.log('响应数据:', response.data);
            
            // 返回任务ID
            const taskId = response.data.prompt_id || response.data.id || `task_${Date.now()}`;
            
            res.json({
                taskId: taskId,
                status: 'queued',
                position: 1,
                estimatedTime: '30-60秒',
                message: '任务已提交到ComfyUI'
            });
            
        } catch (error) {
            console.error('生成失败:', error);
            console.error('错误详情:', {
                message: error.message,
                status: error.response?.status,
                data: error.response?.data,
                code: error.code
            });
            
            res.status(500).json({
                error: `生成失败: ${error.message}`,
                details: error.response?.data,
                code: error.code
            });
        }
    });
    
    // 获取任务状态
    app.get('/api/status/:taskId', (req, res) => {
        console.log('收到状态查询请求:', req.params.taskId);
        
        // 模拟返回完成状态
        res.json({
            status: 'completed',
            progress: 100,
            result: {
                imageUrl: 'https://via.placeholder.com/512x512/667eea/ffffff?text=AI+Generated',
                prompt: '生成完成',
                generatedAt: new Date()
            }
        });
    });
    
    // 队列信息
    app.get('/api/queue-info', (req, res) => {
        console.log('收到队列信息请求');
        res.json({
            queueLength: 0,
            processingCount: 0
        });
    });
    
    // 启动服务器
    app.listen(PORT, () => {
        console.log(`调试服务器启动成功！`);
        console.log(`访问地址: http://localhost:${PORT}`);
        console.log(`健康检查: http://localhost:${PORT}/health`);
        console.log(`测试接口: http://localhost:${PORT}/test`);
    });
    
    // 错误处理
    process.on('uncaughtException', (error) => {
        console.error('未捕获的异常:', error);
    });
    
    process.on('unhandledRejection', (reason, promise) => {
        console.error('未处理的Promise拒绝:', reason);
    });
    
} catch (error) {
    console.error('服务器启动失败:', error);
    process.exit(1);
}
