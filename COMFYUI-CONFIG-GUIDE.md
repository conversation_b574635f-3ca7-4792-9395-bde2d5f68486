# ComfyUI服务器配置指南

## 🎯 问题解决

您遇到的两个问题已经完全解决：

### 1. ✅ 前端队列信息404错误
- **问题**: `App.vue?t=1751252294498:98 获取队列信息失败: Error: Request failed with status code 404`
- **解决**: 后端API路径已修复，队列信息接口正常工作

### 2. ✅ ComfyUI域名动态配置
- **问题**: ComfyUI服务器重启后域名前缀会变化（如 `c14ed69d45f844aaa00fb5115311e6d5` 部分）
- **解决**: 添加了完整的ComfyUI配置管理系统

## 🚀 新功能介绍

### ComfyUI配置管理系统
现在您可以通过界面轻松管理ComfyUI服务器配置，无需修改代码！

#### **主要特性**
- 🔧 **可视化配置**: 通过友好的界面配置ComfyUI地址
- 🔍 **自动检测**: 智能检测可用的ComfyUI服务器
- ⚡ **快速更新**: 域名前缀变化时一键更新
- 💾 **持久化存储**: 配置保存在服务器本地文件
- 🔗 **连接测试**: 实时验证ComfyUI连接状态

## 📱 使用方法

### 1. 访问配置界面
在主界面顶部，点击**黄色的监控器图标**（⚙️旁边）打开ComfyUI配置对话框

### 2. 配置方式

#### **方式1: 自动检测（推荐）**
1. 点击"自动检测"按钮
2. 系统会自动扫描常用的ComfyUI地址
3. 检测成功后点击"保存配置"

#### **方式2: 手动配置**
1. 在"ComfyUI地址"输入框中输入完整地址
   ```
   https://c14ed69d45f844aaa00fb5115311e6d5--8088.ap-shanghai.cloudstudio.club
   ```
2. 点击"测试连接"验证地址
3. 连接成功后点击"保存配置"

#### **方式3: 快速更新域名前缀**
当只有域名前缀变化时：
1. 在"域名前缀快速更新"区域
2. 输入新的前缀（如：`abc123def456`）
3. 点击"快速更新"
4. 测试连接后保存

### 3. 状态监控
主界面会实时显示：
- **前端API**: 前端与后端的连接状态
- **ComfyUI**: 后端与ComfyUI的连接状态

## 🔧 技术实现

### 后端配置管理
- **配置文件**: `backend/comfyui-config.json`
- **自动保存**: 配置变更自动持久化
- **API接口**: 提供完整的配置管理API

### 前端配置界面
- **实时状态**: 显示当前配置和连接状态
- **智能验证**: 自动验证URL格式和连接性
- **用户友好**: 提供多种配置方式

### 动态URL生成
```javascript
// 自动使用配置的ComfyUI地址
const promptUrl = comfyUIConfig.getPromptUrl()
const queueUrl = comfyUIConfig.getQueueUrl()
const historyUrl = comfyUIConfig.getHistoryUrl(promptId)
const imageUrl = comfyUIConfig.getViewUrl(filename, subfolder, type)
```

## 📋 配置示例

### CloudStudio环境
```json
{
  "baseURL": "https://c14ed69d45f844aaa00fb5115311e6d5--8088.ap-shanghai.cloudstudio.club",
  "timeout": 30000
}
```

### 本地环境
```json
{
  "baseURL": "http://localhost:8188",
  "timeout": 30000
}
```

## 🔍 故障排除

### 常见问题

1. **ComfyUI连接失败**
   - 检查ComfyUI服务是否正常运行
   - 确认地址格式正确
   - 尝试自动检测功能

2. **域名前缀更新后仍连接失败**
   - 确认新的前缀正确
   - 检查完整URL是否可访问
   - 重新测试连接

3. **配置保存失败**
   - 检查后端服务是否正常
   - 确认有文件写入权限
   - 查看后端日志

### 调试信息
后端日志会显示：
```
✅ ComfyUI配置已加载: https://xxx--8088.ap-shanghai.cloudstudio.club
🔄 ComfyUI配置已更新: https://yyy--8088.ap-shanghai.cloudstudio.club
🔍 验证ComfyUI配置: https://yyy--8088.ap-shanghai.cloudstudio.club/api/queue
```

## 🎯 使用场景

### 开发环境
- 使用本地ComfyUI: `http://localhost:8188`
- 自动检测本地服务

### CloudStudio环境
- 服务器重启后域名前缀变化
- 使用快速更新功能一键切换
- 自动检测新的可用地址

### 生产环境
- 配置固定的ComfyUI服务器地址
- 定期检查连接状态
- 支持负载均衡和故障转移

## 🎉 总结

现在您的AI图片生成器具备了完整的ComfyUI配置管理能力：

✅ **问题已解决**
- 前端队列信息API正常工作
- ComfyUI地址可通过界面配置

✅ **新增功能**
- 可视化ComfyUI配置管理
- 自动检测和快速更新
- 实时连接状态监控

✅ **使用便捷**
- 无需修改代码
- 界面操作简单
- 配置自动保存

当ComfyUI服务器重启导致域名变化时，只需：
1. 点击黄色监控器图标
2. 使用自动检测或快速更新功能
3. 保存新配置

就可以继续正常使用AI图片生成功能了！🎨✨
