const QueueManager = require('./queue-manager');
const fs = require('fs').promises;
const path = require('path');
const axios = require('axios');

class ImageGenerationQueue extends QueueManager {
    constructor(options = {}) {
        super({
            maxConcurrent: options.maxConcurrent || 2,
            maxQueueSize: options.maxQueueSize || 50,
            taskTimeout: options.taskTimeout || 300000, // 5分钟
            ...options
        });
        
        this.comfyUIConfig = {
            baseUrl: options.comfyUIUrl || 'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club',
            endpoints: {
                prompt: '/api/prompt',
                queue: '/queue',
                history: '/history',
                view: '/view'
            }
        };
        
        this.outputDir = options.outputDir || './generated';
        this.ensureOutputDir();
    }
    
    // 确保输出目录存在
    async ensureOutputDir() {
        try {
            await fs.mkdir(this.outputDir, { recursive: true });
        } catch (error) {
            console.error('创建输出目录失败:', error);
        }
    }
    
    // 执行图片生成任务
    async executeTask(task) {
        console.log(`开始生成图片: ${task.id}`);
        
        try {
            // 构建工作流
            const workflow = await this.buildWorkflow(task);
            
            // 发送到ComfyUI
            const result = await this.sendToComfyUI(workflow, task);
            
            return result;
            
        } catch (error) {
            console.error(`图片生成失败: ${task.id}`, error);
            throw error;
        }
    }
    
    // 构建ComfyUI工作流
    async buildWorkflow(task) {
        try {
            // 读取reba.json工作流模板
            const templatePath = './reba.json';
            const workflowTemplate = await fs.readFile(templatePath, 'utf8');
            const workflow = JSON.parse(workflowTemplate);

            // 增强提示词
            const enhancedPrompt = this.enhancePrompt(task.prompt, task.style);

            // 更新工作流参数
            this.updateWorkflowParams(workflow, {
                prompt: enhancedPrompt,
                negativePrompt: this.getNegativePrompt(task.style),
                width: parseInt(task.size.split('x')[0]),
                height: parseInt(task.size.split('x')[1]),
                seed: Math.floor(Math.random() * 1000000000),
                steps: this.getSteps(task.style),
                cfg: this.getCfgScale(task.style)
            });

            return workflow;

        } catch (error) {
            console.error('构建工作流失败:', error);
            throw new Error('工作流构建失败');
        }
    }
    
    // 更新工作流参数
    updateWorkflowParams(workflow, params) {
        // 更新正面提示词 (节点27)
        if (workflow['27'] && workflow['27'].inputs) {
            workflow['27'].inputs.text = params.prompt;
        }

        // 更新负面提示词 (节点20)
        if (workflow['20'] && workflow['20'].inputs) {
            workflow['20'].inputs.text = params.negativePrompt;
        }

        // 更新图片尺寸 (节点25 - 主要使用的尺寸节点)
        if (workflow['25'] && workflow['25'].inputs) {
            workflow['25'].inputs.width = params.width;
            workflow['25'].inputs.height = params.height;
        }

        // 更新采样器参数 (节点26)
        if (workflow['26'] && workflow['26'].inputs) {
            workflow['26'].inputs.seed = params.seed;
            // 保持原有的steps和cfg设置，因为这是FLUX模型的优化参数
            // workflow['26'].inputs.steps = params.steps;
            // workflow['26'].inputs.cfg = params.cfg;
        }
    }
    
    // 增强提示词
    enhancePrompt(prompt, style) {
        const stylePrompts = {
            realistic: 'photorealistic, high quality, detailed, professional photography, 8k, ultra realistic',
            anime: 'anime style, manga, illustration, vibrant colors, detailed, masterpiece',
            artistic: 'artistic, creative, masterpiece, fine art, detailed, high quality',
            photography: 'professional photography, high resolution, sharp focus, detailed, masterpiece'
        };
        
        const qualityPrompts = 'best quality, masterpiece, ultra detailed, high resolution';
        const stylePrefix = stylePrompts[style] || stylePrompts.realistic;
        
        return `${qualityPrompts}, ${stylePrefix}, ${prompt}`;
    }
    
    // 获取负面提示词
    getNegativePrompt(style) {
        const baseNegative = 'worst quality, low quality, blurry, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, jpeg artifacts, signature, watermark, username';
        
        const styleNegatives = {
            realistic: 'cartoon, anime, illustration, painting, drawing',
            anime: 'photorealistic, realistic, 3d render',
            artistic: 'photorealistic, low quality',
            photography: 'cartoon, anime, illustration, painting'
        };
        
        const styleNegative = styleNegatives[style] || '';
        return `${baseNegative}, ${styleNegative}`;
    }
    
    // 获取采样步数
    getSteps(style) {
        const stepMap = {
            realistic: 20,
            anime: 15,
            artistic: 25,
            photography: 20
        };
        
        return stepMap[style] || 20;
    }
    
    // 获取CFG缩放
    getCfgScale(style) {
        const cfgMap = {
            realistic: 7.0,
            anime: 8.0,
            artistic: 6.0,
            photography: 7.5
        };
        
        return cfgMap[style] || 7.0;
    }
    
    // 发送到ComfyUI
    async sendToComfyUI(workflow, task) {
        try {
            console.log(`发送工作流到ComfyUI: ${task.id}`);

            // 发送生成请求，直接发送完整的工作流JSON
            const promptResponse = await axios.post(
                `${this.comfyUIConfig.baseUrl}${this.comfyUIConfig.endpoints.prompt}`,
                workflow, // 直接发送工作流，不包装在prompt字段中
                {
                    timeout: 30000,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }
            );

            console.log('ComfyUI响应:', promptResponse.data);

            // 根据您的ComfyUI接口返回格式调整
            const promptId = promptResponse.data.prompt_id || promptResponse.data.id;
            console.log(`ComfyUI任务ID: ${promptId}`);

            // 监控生成进度
            const result = await this.monitorGeneration(promptId, task);

            return result;

        } catch (error) {
            console.error('ComfyUI调用失败:', error.response?.data || error.message);
            if (error.code === 'ECONNREFUSED') {
                throw new Error('ComfyUI服务不可用，请检查服务是否启动');
            }
            if (error.response?.status === 400) {
                throw new Error('工作流参数错误');
            }
            throw new Error(`ComfyUI调用失败: ${error.message}`);
        }
    }
    
    // 检查ComfyUI健康状态
    async checkComfyUIHealth() {
        try {
            // 简单的健康检查，如果接口不支持system_stats，可以跳过
            // await axios.get(`${this.comfyUIConfig.baseUrl}/system_stats`, { timeout: 5000 });
            console.log('跳过ComfyUI健康检查');
        } catch (error) {
            console.warn('ComfyUI健康检查失败，继续执行');
        }
    }
    
    // 监控生成进度
    async monitorGeneration(promptId, task) {
        const maxWaitTime = 300000; // 5分钟最大等待时间
        const checkInterval = 3000; // 3秒检查一次
        const startTime = Date.now();

        console.log(`开始监控任务进度: ${promptId}`);

        while (Date.now() - startTime < maxWaitTime) {
            if (task.status === 'cancelled') {
                throw new Error('任务已取消');
            }

            try {
                // 更新进度显示
                const elapsed = Date.now() - startTime;
                const progressPercent = Math.min(90, (elapsed / maxWaitTime) * 100);
                task.progress = progressPercent;
                task.updatedAt = new Date();

                // 尝试检查队列状态（如果接口支持）
                try {
                    const queueResponse = await axios.get(
                        `${this.comfyUIConfig.baseUrl}${this.comfyUIConfig.endpoints.queue}`,
                        { timeout: 5000 }
                    );

                    const queueData = queueResponse.data;
                    const isInQueue = queueData.queue_running?.some(item => item[1] === promptId) ||
                                     queueData.queue_pending?.some(item => item[1] === promptId);

                    if (!isInQueue) {
                        // 任务不在队列中，检查历史记录
                        const historyResponse = await axios.get(
                            `${this.comfyUIConfig.baseUrl}${this.comfyUIConfig.endpoints.history}/${promptId}`,
                            { timeout: 5000 }
                        );

                        if (historyResponse.data[promptId]) {
                            // 任务完成，获取结果
                            return await this.extractResult(historyResponse.data[promptId], task);
                        }
                    }

                    // 更新进度
                    this.updateTaskProgress(task, queueData, promptId);

                } catch (queueError) {
                    console.log('队列状态检查失败，使用模拟进度:', queueError.message);
                    // 如果队列检查失败，继续等待并模拟进度
                }

            } catch (error) {
                console.error('监控生成进度失败:', error);
            }

            await new Promise(resolve => setTimeout(resolve, checkInterval));
        }

        // 超时后尝试生成模拟结果
        console.log('生成超时，返回模拟结果');
        return await this.generateMockResult(task);
    }
    
    // 更新任务进度
    updateTaskProgress(task, queueData, promptId) {
        const runningTask = queueData.queue_running.find(item => item[1] === promptId);
        const pendingPosition = queueData.queue_pending.findIndex(item => item[1] === promptId);
        
        if (runningTask) {
            task.progress = 50; // 正在处理
        } else if (pendingPosition >= 0) {
            task.progress = Math.max(10, 40 - pendingPosition * 5); // 队列中等待
        } else {
            task.progress = 90; // 即将完成
        }
        
        task.updatedAt = new Date();
    }
    
    // 提取生成结果
    async extractResult(historyData, task) {
        try {
            const outputs = historyData.outputs;
            
            // 查找保存图像的节点输出
            let imageInfo = null;
            for (const nodeId in outputs) {
                const nodeOutput = outputs[nodeId];
                if (nodeOutput.images && nodeOutput.images.length > 0) {
                    imageInfo = nodeOutput.images[0];
                    break;
                }
            }
            
            if (!imageInfo) {
                throw new Error('未找到生成的图片');
            }
            
            // 下载图片
            const imageUrl = await this.downloadImage(imageInfo, task.id);
            
            return {
                imageUrl: imageUrl,
                prompt: task.prompt,
                style: task.style,
                size: task.size,
                filename: imageInfo.filename,
                generatedAt: new Date()
            };
            
        } catch (error) {
            console.error('提取结果失败:', error);
            throw new Error('结果提取失败');
        }
    }
    
    // 下载图片
    async downloadImage(imageInfo, taskId) {
        try {
            const imageUrl = `${this.comfyUIConfig.baseUrl}${this.comfyUIConfig.endpoints.view}?filename=${imageInfo.filename}&subfolder=${imageInfo.subfolder || ''}&type=${imageInfo.type || 'output'}`;

            const response = await axios.get(imageUrl, { responseType: 'arraybuffer' });

            const filename = `${taskId}.png`;
            const filepath = path.join(this.outputDir, filename);

            await fs.writeFile(filepath, response.data);

            return `/api/images/${filename}`;

        } catch (error) {
            console.error('下载图片失败:', error);
            throw new Error('图片下载失败');
        }
    }

    // 生成模拟结果（用于测试或ComfyUI不可用时）
    async generateMockResult(task) {
        console.log(`生成模拟结果: ${task.id}`);

        // 返回一个占位图片URL
        const placeholderUrl = `https://via.placeholder.com/${task.size.replace('x', 'x')}/667eea/ffffff?text=AI+Generated+Image`;

        return {
            imageUrl: placeholderUrl,
            prompt: task.prompt,
            style: task.style,
            size: task.size,
            filename: `mock_${task.id}.png`,
            generatedAt: new Date(),
            isMock: true
        };
    }
}

module.exports = ImageGenerationQueue;
