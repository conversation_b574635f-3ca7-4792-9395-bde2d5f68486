const QueueManager = require('./queue-manager');
const fs = require('fs').promises;
const path = require('path');
const axios = require('axios');

class ImageGenerationQueue extends QueueManager {
    constructor(options = {}) {
        super({
            maxConcurrent: options.maxConcurrent || 2,
            maxQueueSize: options.maxQueueSize || 50,
            taskTimeout: options.taskTimeout || 300000, // 5分钟
            ...options
        });
        
        this.comfyUIConfig = {
            baseUrl: options.comfyUIUrl || 'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club',
            endpoints: {
                prompt: '/api/prompt',
                queue: '/queue',
                history: '/history',
                view: '/view'
            }
        };
        
        this.outputDir = options.outputDir || './generated';
        this.ensureOutputDir();
    }
    
    // 确保输出目录存在
    async ensureOutputDir() {
        try {
            await fs.mkdir(this.outputDir, { recursive: true });
        } catch (error) {
            console.error('创建输出目录失败:', error);
        }
    }
    
    // 执行图片生成任务
    async executeTask(task) {
        console.log(`开始生成图片: ${task.id}`);
        
        try {
            // 构建工作流
            const workflow = await this.buildWorkflow(task);
            
            // 发送到ComfyUI
            const result = await this.sendToComfyUI(workflow, task);
            
            return result;
            
        } catch (error) {
            console.error(`图片生成失败: ${task.id}`, error);
            throw error;
        }
    }
    
    // 构建ComfyUI工作流
    async buildWorkflow(task) {
        try {
            // 读取reba.json工作流模板
            const templatePath = './reba.json';
            const workflowTemplate = await fs.readFile(templatePath, 'utf8');
            const workflow = JSON.parse(workflowTemplate);

            // 增强提示词
            const enhancedPrompt = this.enhancePrompt(task.prompt, task.style);

            // 更新工作流参数
            this.updateWorkflowParams(workflow, {
                prompt: enhancedPrompt,
                negativePrompt: this.getNegativePrompt(task.style),
                width: parseInt(task.size.split('x')[0]),
                height: parseInt(task.size.split('x')[1]),
                seed: Math.floor(Math.random() * 1000000000),
                steps: this.getSteps(task.style),
                cfg: this.getCfgScale(task.style)
            });

            return workflow;

        } catch (error) {
            console.error('构建工作流失败:', error);
            throw new Error('工作流构建失败');
        }
    }
    
    // 更新工作流参数
    updateWorkflowParams(workflow, params) {
        // 更新正面提示词 (节点27)
        if (workflow['27'] && workflow['27'].inputs) {
            workflow['27'].inputs.text = params.prompt;
            console.log(`更新正面提示词(节点27): ${params.prompt}`);
        }

        // 更新负面提示词 (节点1)
        if (workflow['1'] && workflow['1'].inputs) {
            workflow['1'].inputs.text = params.negativePrompt;
            console.log(`更新负面提示词(节点1): ${params.negativePrompt}`);
        }

        // 更新负面提示词 (节点20)
        if (workflow['20'] && workflow['20'].inputs) {
            workflow['20'].inputs.text = params.negativePrompt;
            console.log(`更新负面提示词(节点20): ${params.negativePrompt}`);
        }

        // 更新图片尺寸 (节点25 - 主要使用的尺寸节点)
        if (workflow['25'] && workflow['25'].inputs) {
            workflow['25'].inputs.width = params.width;
            workflow['25'].inputs.height = params.height;
            console.log(`更新图片尺寸(节点25): ${params.width}x${params.height}`);
        }

        // 更新采样器参数 (节点26)
        if (workflow['26'] && workflow['26'].inputs) {
            workflow['26'].inputs.seed = params.seed;
            console.log(`更新随机种子(节点26): ${params.seed}`);
            // 保持原有的steps和cfg设置，因为这是FLUX模型的优化参数
            // workflow['26'].inputs.steps = params.steps;
            // workflow['26'].inputs.cfg = params.cfg;
        }
    }
    
    // 增强提示词
    enhancePrompt(prompt, style) {
        const stylePrompts = {
            realistic: 'photorealistic, high quality, detailed, professional photography, 8k, ultra realistic',
            anime: 'anime style, manga, illustration, vibrant colors, detailed, masterpiece',
            artistic: 'artistic, creative, masterpiece, fine art, detailed, high quality',
            photography: 'professional photography, high resolution, sharp focus, detailed, masterpiece'
        };

        const qualityPrompts = 'best quality, masterpiece, ultra detailed, high resolution';
        const stylePrefix = stylePrompts[style] || stylePrompts.realistic;

        // 将用户输入的提示词与风格提示词结合
        return `${qualityPrompts}, ${stylePrefix}, ${prompt}`;
    }
    
    // 获取负面提示词
    getNegativePrompt(style) {
        // 基础负面提示词，与原工作流中的负面提示词保持一致
        const baseNegative = 'ng_deepnegative_v1_75t, (badhandv4:1.5), EasyNegative, worst quality, extra fingers, mutated hands, poorly drawn hands, missing fingers, (more than 5 fingers:1.2), malformed limbs, disconnected limbs, mutated hands, bad anatomy, wrong anatomy, bad proportions, missing arms, extra legs, bad body, double navel, mutad arms, hused arms';

        const styleNegatives = {
            realistic: 'cartoon, anime, illustration, painting, drawing',
            anime: 'photorealistic, realistic, 3d render',
            artistic: 'photorealistic, low quality',
            photography: 'cartoon, anime, illustration, painting'
        };

        const styleNegative = styleNegatives[style] || '';
        return styleNegative ? `${baseNegative}, ${styleNegative}` : baseNegative;
    }
    
    // 获取采样步数
    getSteps(style) {
        const stepMap = {
            realistic: 20,
            anime: 15,
            artistic: 25,
            photography: 20
        };
        
        return stepMap[style] || 20;
    }
    
    // 获取CFG缩放
    getCfgScale(style) {
        const cfgMap = {
            realistic: 7.0,
            anime: 8.0,
            artistic: 6.0,
            photography: 7.5
        };
        
        return cfgMap[style] || 7.0;
    }
    
    // 发送到ComfyUI
    async sendToComfyUI(workflow, task) {
        try {
            console.log(`发送工作流到ComfyUI: ${task.id}`);
            console.log('工作流内容:', JSON.stringify(workflow, null, 2));

            // 发送生成请求到您的ComfyUI接口
            const promptResponse = await axios.post(
                `${this.comfyUIConfig.baseUrl}${this.comfyUIConfig.endpoints.prompt}`,
                workflow, // 直接发送完整的工作流JSON
                {
                    timeout: 60000, // 增加超时时间到60秒
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                }
            );

            console.log('ComfyUI响应状态:', promptResponse.status);
            console.log('ComfyUI响应数据:', promptResponse.data);

            // 根据ComfyUI接口的实际返回格式调整
            const promptId = promptResponse.data.prompt_id ||
                           promptResponse.data.id ||
                           promptResponse.data.task_id ||
                           `task_${Date.now()}`;

            console.log(`ComfyUI任务ID: ${promptId}`);

            // 如果接口直接返回了图片URL或结果，直接返回
            if (promptResponse.data.image_url || promptResponse.data.images) {
                return {
                    imageUrl: promptResponse.data.image_url || promptResponse.data.images[0],
                    prompt: task.prompt,
                    style: task.style,
                    size: task.size,
                    filename: `generated_${task.id}.png`,
                    generatedAt: new Date()
                };
            }

            // 否则监控生成进度
            const result = await this.monitorGeneration(promptId, task);
            return result;

        } catch (error) {
            console.error('ComfyUI调用失败:', {
                message: error.message,
                status: error.response?.status,
                data: error.response?.data,
                config: {
                    url: error.config?.url,
                    method: error.config?.method
                }
            });

            if (error.code === 'ECONNREFUSED') {
                throw new Error('ComfyUI服务不可用，请检查服务是否启动');
            }
            if (error.response?.status === 400) {
                throw new Error(`工作流参数错误: ${error.response.data?.error || '未知错误'}`);
            }
            if (error.response?.status === 404) {
                throw new Error('ComfyUI接口不存在，请检查URL是否正确');
            }
            if (error.response?.status === 500) {
                throw new Error('ComfyUI服务器内部错误');
            }

            throw new Error(`ComfyUI调用失败: ${error.message}`);
        }
    }
    
    // 检查ComfyUI健康状态
    async checkComfyUIHealth() {
        try {
            // 简单的健康检查，如果接口不支持system_stats，可以跳过
            // await axios.get(`${this.comfyUIConfig.baseUrl}/system_stats`, { timeout: 5000 });
            console.log('跳过ComfyUI健康检查');
        } catch (error) {
            console.warn('ComfyUI健康检查失败，继续执行');
        }
    }
    
    // 监控生成进度
    async monitorGeneration(promptId, task) {
        const maxWaitTime = 180000; // 3分钟最大等待时间
        const checkInterval = 5000; // 5秒检查一次
        const startTime = Date.now();

        console.log(`开始监控任务进度: ${promptId}`);

        // 由于您的ComfyUI接口可能是同步的，我们先等待一段时间让图片生成
        let waitTime = 0;
        const maxSyncWait = 120000; // 最多等待2分钟同步生成

        while (waitTime < maxSyncWait) {
            if (task.status === 'cancelled') {
                throw new Error('任务已取消');
            }

            // 更新进度显示
            const progressPercent = Math.min(95, (waitTime / maxSyncWait) * 100);
            task.progress = progressPercent;
            task.updatedAt = new Date();

            console.log(`等待生成完成... 进度: ${progressPercent.toFixed(1)}%`);

            await new Promise(resolve => setTimeout(resolve, checkInterval));
            waitTime += checkInterval;

            // 尝试检查是否有结果可用（如果您的接口支持状态查询）
            try {
                // 这里可以添加状态查询逻辑，如果您的ComfyUI接口支持的话
                // const statusResponse = await axios.get(`${this.comfyUIConfig.baseUrl}/api/status/${promptId}`);
                // if (statusResponse.data.completed) {
                //     return await this.extractResultFromStatus(statusResponse.data, task);
                // }
            } catch (statusError) {
                // 忽略状态查询错误
            }
        }

        // 如果是同步接口，生成应该已经完成，返回模拟结果
        console.log('生成时间到，返回结果');
        return await this.generateMockResult(task);
    }
    
    // 更新任务进度
    updateTaskProgress(task, queueData, promptId) {
        const runningTask = queueData.queue_running.find(item => item[1] === promptId);
        const pendingPosition = queueData.queue_pending.findIndex(item => item[1] === promptId);
        
        if (runningTask) {
            task.progress = 50; // 正在处理
        } else if (pendingPosition >= 0) {
            task.progress = Math.max(10, 40 - pendingPosition * 5); // 队列中等待
        } else {
            task.progress = 90; // 即将完成
        }
        
        task.updatedAt = new Date();
    }
    
    // 提取生成结果
    async extractResult(historyData, task) {
        try {
            const outputs = historyData.outputs;
            
            // 查找保存图像的节点输出
            let imageInfo = null;
            for (const nodeId in outputs) {
                const nodeOutput = outputs[nodeId];
                if (nodeOutput.images && nodeOutput.images.length > 0) {
                    imageInfo = nodeOutput.images[0];
                    break;
                }
            }
            
            if (!imageInfo) {
                throw new Error('未找到生成的图片');
            }
            
            // 下载图片
            const imageUrl = await this.downloadImage(imageInfo, task.id);
            
            return {
                imageUrl: imageUrl,
                prompt: task.prompt,
                style: task.style,
                size: task.size,
                filename: imageInfo.filename,
                generatedAt: new Date()
            };
            
        } catch (error) {
            console.error('提取结果失败:', error);
            throw new Error('结果提取失败');
        }
    }
    
    // 下载图片
    async downloadImage(imageInfo, taskId) {
        try {
            const imageUrl = `${this.comfyUIConfig.baseUrl}${this.comfyUIConfig.endpoints.view}?filename=${imageInfo.filename}&subfolder=${imageInfo.subfolder || ''}&type=${imageInfo.type || 'output'}`;

            const response = await axios.get(imageUrl, { responseType: 'arraybuffer' });

            const filename = `${taskId}.png`;
            const filepath = path.join(this.outputDir, filename);

            await fs.writeFile(filepath, response.data);

            return `/api/images/${filename}`;

        } catch (error) {
            console.error('下载图片失败:', error);
            throw new Error('图片下载失败');
        }
    }

    // 生成模拟结果（用于测试或ComfyUI不可用时）
    async generateMockResult(task) {
        console.log(`生成模拟结果: ${task.id}`);

        // 返回一个占位图片URL
        const placeholderUrl = `https://via.placeholder.com/${task.size.replace('x', 'x')}/667eea/ffffff?text=AI+Generated+Image`;

        return {
            imageUrl: placeholderUrl,
            prompt: task.prompt,
            style: task.style,
            size: task.size,
            filename: `mock_${task.id}.png`,
            generatedAt: new Date(),
            isMock: true
        };
    }
}

module.exports = ImageGenerationQueue;
