/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
  line-height: 1.6;
}

#app {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* 自定义Element Plus样式 */
.el-card {
  border-radius: 20px !important;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1) !important;
}

.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  border-radius: 15px !important;
  padding: 18px 30px !important;
  font-size: 18px !important;
  font-weight: 600 !important;
}

.el-button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3) !important;
}

.el-textarea__inner {
  border-radius: 15px !important;
  border: 2px solid #e1e5e9 !important;
  background: #fafbfc !important;
  font-size: 16px !important;
  padding: 20px !important;
  transition: all 0.3s ease !important;
}

.el-textarea__inner:focus {
  border-color: #667eea !important;
  background: white !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

.negative-textarea .el-textarea__inner {
  border-color: #f0ad4e !important;
  background: #fef9f0 !important;
}

.negative-textarea .el-textarea__inner:focus {
  border-color: #e67e22 !important;
  box-shadow: 0 0 0 3px rgba(230, 126, 34, 0.1) !important;
}

.el-select {
  width: 100% !important;
}

.el-select .el-input__inner {
  border-radius: 10px !important;
  border: 2px solid #e1e5e9 !important;
  padding: 12px 15px !important;
  font-size: 14px !important;
}

.el-progress-bar__outer {
  border-radius: 4px !important;
}

.el-progress-bar__inner {
  background: linear-gradient(90deg, #667eea, #764ba2) !important;
  border-radius: 4px !important;
}

/* 动画效果 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active, .slide-up-leave-active {
  transition: all 0.5s ease;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  #app {
    padding: 15px;
  }
  
  .el-card {
    margin: 0 !important;
  }
  
  .el-button--primary {
    width: 100% !important;
    font-size: 16px !important;
    padding: 15px 25px !important;
  }
}
