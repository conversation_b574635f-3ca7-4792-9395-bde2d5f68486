const express = require('express');
const cors = require('cors');
const axios = require('axios');
const fs = require('fs').promises;

const app = express();
const PORT = 3005;

console.log('启动真实ComfyUI服务器...');

// 任务状态存储
const taskStatus = new Map();

// 基础中间件
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// 健康检查
app.get('/health', (req, res) => {
    console.log('收到健康检查请求');
    res.json({ status: 'ok', timestamp: new Date() });
});

// 生成图片API（真实ComfyUI调用）
app.post('/api/generate', async (req, res) => {
    console.log('收到生成图片请求:', req.body);
    
    try {
        const { prompt, negativePrompt, size } = req.body;
        
        if (!prompt || prompt.trim().length < 10) {
            return res.status(400).json({ error: '描述至少需要10个字符' });
        }
        
        console.log('开始处理真实ComfyUI生成请求...');
        console.log('- 正面提示词:', prompt);
        console.log('- 反向提示词:', negativePrompt || '(无)');
        console.log('- 图片尺寸:', size);
        
        // 生成任务ID
        const taskId = `real_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
        
        // 初始化任务状态
        taskStatus.set(taskId, {
            status: 'queued',
            progress: 5,
            position: 1,
            estimatedTime: '60-120秒',
            prompt: prompt,
            negativePrompt: negativePrompt,
            size: size,
            createdAt: new Date()
        });
        
        console.log(`任务创建成功: ${taskId}`);
        
        // 异步处理ComfyUI请求
        processRealComfyUI(taskId, prompt, negativePrompt, size).catch(error => {
            console.error(`异步处理失败: ${taskId}`, error);
        });
        
        // 立即返回任务ID
        res.json({
            taskId: taskId,
            status: 'queued',
            position: 1,
            estimatedTime: '60-120秒',
            message: '任务已提交到真实ComfyUI，正在处理中...'
        });
        
    } catch (error) {
        console.error('生成失败:', error.message);
        res.status(500).json({
            error: `生成失败: ${error.message}`
        });
    }
});

// 状态查询
app.get('/api/status/:taskId', (req, res) => {
    const taskId = req.params.taskId;
    console.log('收到状态查询:', taskId);
    
    const task = taskStatus.get(taskId);
    
    if (!task) {
        return res.status(404).json({ error: '任务不存在' });
    }
    
    res.json({
        status: task.status,
        progress: task.progress || 0,
        position: task.position || 0,
        estimatedTime: task.estimatedTime || '处理中',
        result: task.result,
        error: task.error
    });
});

// 队列信息
app.get('/api/queue-info', (req, res) => {
    console.log('收到队列信息请求');
    
    const queuedTasks = Array.from(taskStatus.values()).filter(task => 
        task.status === 'queued' || task.status === 'processing'
    );
    
    res.json({ 
        queueLength: queuedTasks.length,
        processingCount: queuedTasks.filter(task => task.status === 'processing').length
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`真实ComfyUI服务器运行在 http://localhost:${PORT}`);
});

// 真实ComfyUI处理函数
async function processRealComfyUI(taskId, prompt, negativePrompt, size) {
    console.log(`🚀 开始真实ComfyUI处理: ${taskId}`);
    
    const task = taskStatus.get(taskId);
    if (!task) return;
    
    try {
        // 1. 读取工作流文件
        console.log('📖 读取工作流文件...');
        const workflowData = await fs.readFile('./reba.json', 'utf8');
        const workflow = JSON.parse(workflowData);
        
        // 2. 更新工作流参数
        console.log('⚙️ 更新工作流参数...');
        
        // 更新正面提示词
        if (workflow['27'] && workflow['27'].inputs) {
            workflow['27'].inputs.text = `best quality, masterpiece, ${prompt}`;
            console.log('✅ 正面提示词已更新');
        }
        
        // 更新负面提示词
        const finalNegativePrompt = negativePrompt ? 
            `${negativePrompt}, worst quality, low quality, blurry, bad anatomy` : 
            'worst quality, low quality, blurry, bad anatomy';
            
        if (workflow['1'] && workflow['1'].inputs) {
            workflow['1'].inputs.text = finalNegativePrompt;
            console.log('✅ 负面提示词(节点1)已更新');
        }
        if (workflow['20'] && workflow['20'].inputs) {
            workflow['20'].inputs.text = finalNegativePrompt;
            console.log('✅ 负面提示词(节点20)已更新');
        }
        
        // 更新图片尺寸
        if (size && workflow['25'] && workflow['25'].inputs) {
            const [width, height] = size.split('x').map(Number);
            workflow['25'].inputs.width = width;
            workflow['25'].inputs.height = height;
            workflow['25'].inputs.batch_size = 1;
            console.log(`✅ 图片尺寸已更新: ${width}x${height}`);
        }
        
        // 生成随机种子
        const seed = Math.floor(Math.random() * 1000000000);
        if (workflow['26'] && workflow['26'].inputs) {
            workflow['26'].inputs.seed = seed;
            console.log('✅ 随机种子已生成:', seed);
        }
        
        // 3. 更新任务状态
        task.status = 'processing';
        task.progress = 20;
        task.estimatedTime = '30-90秒';
        
        // 4. 发送到ComfyUI
        console.log('🌐 发送请求到ComfyUI...');
        const response = await axios.post(
            'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/prompt',
            {
                prompt: workflow
            },
            {
                timeout: 30000,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            }
        );
        
        console.log('✅ ComfyUI响应成功:', response.status);
        console.log('📋 获得prompt_id:', response.data.prompt_id);
        
        const promptId = response.data.prompt_id;
        task.promptId = promptId;
        task.progress = 40;
        
        // 5. 等待ComfyUI完成
        console.log('⏳ 等待ComfyUI处理完成...');
        const imageResult = await waitForComfyUIResult(promptId, task);
        
        // 6. 更新任务状态为完成
        task.status = 'completed';
        task.progress = 100;
        task.result = {
            imageUrl: imageResult.imageUrl,
            prompt: prompt,
            negativePrompt: negativePrompt || '',
            size: size,
            filename: imageResult.filename,
            promptId: promptId,
            generatedAt: new Date()
        };
        
        console.log('🎉 任务完成:', taskId);
        console.log('🖼️ 图片URL:', imageResult.imageUrl);
        
    } catch (error) {
        console.error('❌ ComfyUI处理失败:', taskId, error.message);
        
        task.status = 'failed';
        task.progress = 0;
        task.error = `处理失败: ${error.message}`;
        
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// 等待ComfyUI结果
async function waitForComfyUIResult(promptId, task) {
    const maxWaitTime = 180000; // 3分钟
    const checkInterval = 5000; // 5秒检查一次
    const startTime = Date.now();
    
    console.log(`🔍 开始监控ComfyUI进度: ${promptId}`);
    
    while (Date.now() - startTime < maxWaitTime) {
        try {
            // 更新进度
            const elapsed = Date.now() - startTime;
            const progressPercent = Math.min(95, 40 + (elapsed / maxWaitTime) * 55);
            task.progress = Math.round(progressPercent);
            
            // 检查队列状态
            const queueResponse = await axios.get(
                'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/queue',
                { timeout: 10000 }
            );
            
            const queueData = queueResponse.data;
            const isInQueue = queueData.queue_running?.some(item => item[1] === promptId) ||
                             queueData.queue_pending?.some(item => item[1] === promptId);
            
            if (!isInQueue) {
                console.log('✅ 任务不在队列中，检查历史记录...');
                
                // 检查历史记录
                const historyResponse = await axios.get(
                    `https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/history/${promptId}`,
                    { timeout: 10000 }
                );
                
                const historyData = historyResponse.data;
                
                if (historyData[promptId]) {
                    const outputs = historyData[promptId].outputs;
                    console.log('🔍 找到输出:', Object.keys(outputs));
                    
                    // 查找图片
                    for (const nodeId in outputs) {
                        const nodeOutput = outputs[nodeId];
                        if (nodeOutput.images && nodeOutput.images.length > 0) {
                            const imageInfo = nodeOutput.images[0];
                            console.log('🖼️ 找到图片:', imageInfo);
                            
                            const imageUrl = `https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/view?filename=${imageInfo.filename}&subfolder=${imageInfo.subfolder || ''}&type=${imageInfo.type || 'output'}`;
                            
                            return {
                                imageUrl: imageUrl,
                                filename: imageInfo.filename
                            };
                        }
                    }
                }
            } else {
                console.log('⏳ 任务还在队列中处理...');
            }
            
            await new Promise(resolve => setTimeout(resolve, checkInterval));
            
        } catch (error) {
            console.error('监控失败:', error.message);
            if (error.response?.status !== 404) {
                await new Promise(resolve => setTimeout(resolve, checkInterval));
            }
        }
    }
    
    throw new Error('ComfyUI处理超时');
}
