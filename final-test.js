const axios = require('axios');

async function testFinalAPI() {
    const baseURL = 'http://localhost:3002';
    
    console.log('🚀 开始最终API测试...\n');
    
    try {
        // 1. 健康检查
        console.log('1️⃣ 测试健康检查...');
        const healthResponse = await axios.get(`${baseURL}/health`);
        console.log('✅ 健康检查成功:', healthResponse.data);
        
        // 2. 队列信息
        console.log('\n2️⃣ 测试队列信息...');
        const queueResponse = await axios.get(`${baseURL}/api/queue-info`);
        console.log('✅ 队列信息成功:', queueResponse.data);
        
        // 3. 生成图片
        console.log('\n3️⃣ 测试生成图片API...');
        const generateResponse = await axios.post(`${baseURL}/api/generate`, {
            prompt: 'a beautiful sunset over mountains, peaceful landscape, high quality',
            style: 'realistic',
            size: '512x512'
        });
        console.log('✅ 生成请求成功:', generateResponse.data);
        
        const taskId = generateResponse.data.taskId;
        console.log(`📋 任务ID: ${taskId}`);
        
        // 4. 监控任务状态
        console.log('\n4️⃣ 监控任务状态...');
        let attempts = 0;
        const maxAttempts = 20; // 最多检查20次
        
        while (attempts < maxAttempts) {
            attempts++;
            
            try {
                const statusResponse = await axios.get(`${baseURL}/api/status/${taskId}`);
                const status = statusResponse.data;
                
                console.log(`📊 第${attempts}次检查 - 状态: ${status.status}, 进度: ${status.progress}%`);
                
                if (status.status === 'completed') {
                    console.log('🎉 任务完成!');
                    console.log('📸 结果:', JSON.stringify(status.result, null, 2));
                    break;
                } else if (status.status === 'failed') {
                    console.log('❌ 任务失败:', status.error);
                    break;
                } else {
                    console.log(`⏳ 继续等待... (${status.estimatedTime})`);
                    await new Promise(resolve => setTimeout(resolve, 3000)); // 等待3秒
                }
                
            } catch (statusError) {
                console.log(`⚠️ 状态查询失败: ${statusError.message}`);
                break;
            }
        }
        
        if (attempts >= maxAttempts) {
            console.log('⏰ 达到最大检查次数，停止监控');
        }
        
        console.log('\n🏁 测试完成!');
        
    } catch (error) {
        console.error('\n❌ 测试失败:');
        console.error('错误信息:', error.message);
        
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
        
        if (error.code) {
            console.error('错误代码:', error.code);
        }
        
        throw error;
    }
}

// 运行测试
if (require.main === module) {
    testFinalAPI()
        .then(() => {
            console.log('\n✨ 所有测试通过!');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n💥 测试失败');
            process.exit(1);
        });
}

module.exports = testFinalAPI;
