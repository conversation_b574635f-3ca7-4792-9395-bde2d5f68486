# AI图片生成器 - 部署指南

## 系统要求
- Node.js >= 14.0.0
- npm >= 6.0.0
- PM2 (推荐，用于生产环境进程管理)

## 部署步骤

### 1. 上传文件
将整个 dist 目录上传到服务器

### 2. 安装PM2 (推荐)
```bash
npm install -g pm2
```

### 3. 配置环境变量
复制 .env.example 为 .env 并修改配置：
```bash
cp .env.example .env
nano .env
```

### 4. 启动应用

#### Linux/Mac:
```bash
chmod +x start.sh
./start.sh
```

#### Windows:
```cmd
start.bat
```

#### 手动启动:
```bash
# 使用PM2
pm2 start ecosystem.config.js --env production

# 或直接使用Node.js
NODE_ENV=production PORT=5000 node backend/server.js
```

### 5. 验证部署
访问 http://your-server:5000 检查应用是否正常运行

### 6. 配置反向代理 (可选)
使用Nginx或Apache配置反向代理，将80/443端口转发到5000端口

## PM2 管理命令
```bash
pm2 list                    # 查看进程列表
pm2 logs ai-image-generator # 查看日志
pm2 restart ai-image-generator # 重启应用
pm2 stop ai-image-generator    # 停止应用
pm2 delete ai-image-generator  # 删除应用
```

## 目录结构
```
dist/
├── backend/           # 后端代码和依赖
├── workflow/          # ComfyUI工作流配置
├── logs/             # 日志文件 (运行时创建)
├── package.json      # 项目配置
├── ecosystem.config.js # PM2配置
├── .env.example      # 环境变量示例
├── start.sh          # Linux/Mac启动脚本
└── start.bat         # Windows启动脚本
```
