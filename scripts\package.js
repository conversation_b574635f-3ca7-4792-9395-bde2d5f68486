#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('📦 开始打包项目...\n');

try {
  // 检查dist目录是否存在
  if (!fs.existsSync('dist')) {
    console.log('❌ dist目录不存在，请先运行构建命令: npm run build');
    process.exit(1);
  }

  // 获取项目信息
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const projectName = packageJson.name;
  const version = packageJson.version;
  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
  
  const archiveName = `${projectName}-v${version}-${timestamp}`;

  console.log(`📋 项目信息:`);
  console.log(`   名称: ${projectName}`);
  console.log(`   版本: ${version}`);
  console.log(`   时间: ${timestamp}`);
  console.log(`   包名: ${archiveName}\n`);

  // 创建releases目录
  if (!fs.existsSync('releases')) {
    fs.mkdirSync('releases');
    console.log('✅ 创建releases目录\n');
  }

  // 检查系统是否支持tar命令
  let useZip = false;
  try {
    execSync('tar --version', { stdio: 'ignore' });
  } catch (error) {
    useZip = true;
  }

  if (useZip) {
    // 使用PowerShell创建ZIP文件 (Windows)
    console.log('🗜️ 使用ZIP格式打包...');
    const zipPath = path.join('releases', `${archiveName}.zip`);
    const command = `Compress-Archive -Path "dist\\*" -DestinationPath "${zipPath}" -Force`;
    
    try {
      execSync(`powershell -Command "${command}"`, { stdio: 'inherit' });
      console.log(`✅ ZIP包创建成功: ${zipPath}`);
    } catch (error) {
      // 如果PowerShell失败，尝试使用Node.js创建ZIP
      console.log('⚠️ PowerShell失败，使用Node.js创建ZIP...');
      createZipWithNode(zipPath);
    }
  } else {
    // 使用tar创建压缩包 (Linux/Mac)
    console.log('🗜️ 使用tar.gz格式打包...');
    const tarPath = path.join('releases', `${archiveName}.tar.gz`);
    execSync(`tar -czf "${tarPath}" -C dist .`, { stdio: 'inherit' });
    console.log(`✅ tar.gz包创建成功: ${tarPath}`);
  }

  // 创建发布说明
  const releaseNotes = `# ${projectName} v${version}

## 发布信息
- 版本: ${version}
- 构建时间: ${new Date().toLocaleString('zh-CN')}
- 包含内容: 完整的生产环境部署包

## 部署说明
1. 解压文件到服务器目录
2. 参考 DEPLOYMENT.md 进行部署配置
3. 运行启动脚本开始服务

## 系统要求
- Node.js >= 14.0.0
- npm >= 6.0.0
- PM2 (推荐)

## 快速启动
\`\`\`bash
# Linux/Mac
chmod +x start.sh
./start.sh

# Windows
start.bat
\`\`\`

## 目录结构
\`\`\`
${archiveName}/
├── backend/           # 后端应用和依赖
├── workflow/          # ComfyUI工作流配置
├── DEPLOYMENT.md      # 详细部署指南
├── ecosystem.config.js # PM2配置文件
├── .env.example       # 环境变量示例
├── start.sh          # Linux/Mac启动脚本
└── start.bat         # Windows启动脚本
\`\`\`

## 支持
如有问题，请查看 DEPLOYMENT.md 或联系开发团队。
`;

  const releaseNotesPath = path.join('releases', `${archiveName}-RELEASE-NOTES.md`);
  fs.writeFileSync(releaseNotesPath, releaseNotes);
  console.log(`✅ 发布说明创建: ${releaseNotesPath}`);

  console.log('\n🎉 打包完成！');
  console.log('\n📦 发布文件:');
  const releaseFiles = fs.readdirSync('releases').filter(file => file.includes(archiveName));
  releaseFiles.forEach(file => {
    const filePath = path.join('releases', file);
    const stats = fs.statSync(filePath);
    const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
    console.log(`   ${file} (${sizeInMB} MB)`);
  });

  console.log('\n🚀 部署步骤:');
  console.log('1. 将压缩包上传到服务器');
  console.log('2. 解压到目标目录');
  console.log('3. 配置环境变量');
  console.log('4. 运行启动脚本');

} catch (error) {
  console.error('❌ 打包失败:', error.message);
  process.exit(1);
}

// 使用Node.js创建ZIP文件的备用方法
function createZipWithNode(zipPath) {
  console.log('使用Node.js内置方法创建ZIP...');
  
  // 这里可以使用archiver或其他ZIP库
  // 为了简化，我们创建一个提示
  console.log('⚠️ 请手动压缩dist目录为ZIP文件');
  console.log(`   目标文件: ${zipPath}`);
  console.log('   或安装7-zip/WinRAR等工具进行压缩');
}
