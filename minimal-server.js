const express = require('express');
const cors = require('cors');
const axios = require('axios');
const fs = require('fs').promises;

const app = express();
const PORT = 3002;

console.log('启动最小化服务器...');

// 任务状态存储
const taskStatus = new Map();

// 基础中间件
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// 简单路由
app.get('/', (req, res) => {
    console.log('收到根路径请求');
    res.send('Hello World!');
});

app.get('/health', (req, res) => {
    console.log('收到健康检查请求');
    res.json({ status: 'ok', timestamp: new Date() });
});

// 生成图片API（集成ComfyUI）
app.post('/api/generate', async (req, res) => {
    console.log('收到生成图片请求:', req.body);

    try {
        const { prompt, negativePrompt, size } = req.body;

        if (!prompt || prompt.trim().length < 10) {
            return res.status(400).json({ error: '描述至少需要10个字符' });
        }

        console.log('开始处理生成请求...');

        // 读取工作流文件
        const workflowData = await fs.readFile('./reba.json', 'utf8');
        const workflow = JSON.parse(workflowData);
        console.log('工作流文件读取成功');

        // 更新正面提示词
        if (workflow['27'] && workflow['27'].inputs) {
            workflow['27'].inputs.text = `best quality, masterpiece, ${prompt}`;
            console.log('更新正面提示词成功');
        }

        // 更新负面提示词
        const finalNegativePrompt = negativePrompt ?
            `${negativePrompt}, worst quality, low quality, blurry, bad anatomy` :
            'worst quality, low quality, blurry, bad anatomy';

        if (workflow['1'] && workflow['1'].inputs) {
            workflow['1'].inputs.text = finalNegativePrompt;
            console.log('更新负面提示词(节点1)成功');
        }
        if (workflow['20'] && workflow['20'].inputs) {
            workflow['20'].inputs.text = finalNegativePrompt;
            console.log('更新负面提示词(节点20)成功');
        }

        // 更新图片尺寸（节点25是主要的尺寸控制节点）
        if (size && workflow['25'] && workflow['25'].inputs) {
            const [width, height] = size.split('x').map(Number);
            workflow['25'].inputs.width = width;
            workflow['25'].inputs.height = height;
            workflow['25'].inputs.batch_size = 1; // 确保只生成一张图片
            console.log(`更新图片尺寸: ${width}x${height}`);
        }

        // 生成随机种子
        if (workflow['26'] && workflow['26'].inputs) {
            workflow['26'].inputs.seed = Math.floor(Math.random() * 1000000000);
        }

        console.log('发送请求到ComfyUI...');

        // 发送到ComfyUI（异步处理）
        console.log('发送异步请求到ComfyUI...');

        // 不等待ComfyUI响应，直接返回任务ID
        const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        // 初始化任务状态
        taskStatus.set(taskId, {
            status: 'queued',
            progress: 5,
            position: 1,
            estimatedTime: '60-120秒',
            prompt: prompt,
            negativePrompt: negativePrompt,
            size: size,
            createdAt: new Date()
        });

        // 异步处理ComfyUI请求
        processComfyUIRequest(workflow, taskId).catch(error => {
            console.error(`异步处理失败: ${taskId}`, error);
        });

        // 立即返回任务ID
        res.json({
            taskId: taskId,
            status: 'queued',
            position: 1,
            estimatedTime: '60-120秒',
            message: '任务已提交到ComfyUI，正在处理中...'
        });


    } catch (error) {
        console.error('生成失败:', error.message);

        if (error.response) {
            console.error('ComfyUI响应错误:', error.response.status, error.response.data);
        }

        res.status(500).json({
            error: `生成失败: ${error.message}`,
            details: error.response?.data
        });
    }
});

// 状态查询
app.get('/api/status/:taskId', (req, res) => {
    const taskId = req.params.taskId;
    console.log('收到状态查询:', taskId);

    const task = taskStatus.get(taskId);

    if (!task) {
        return res.status(404).json({ error: '任务不存在' });
    }

    res.json({
        status: task.status,
        progress: task.progress || 0,
        position: task.position || 0,
        estimatedTime: task.estimatedTime || '处理中',
        result: task.result,
        error: task.error
    });
});

// 队列信息
app.get('/api/queue-info', (req, res) => {
    console.log('收到队列信息请求');
    res.json({ queueLength: 0, processingCount: 0 });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`最小化服务器运行在 http://localhost:${PORT}`);
});

// 错误处理
process.on('uncaughtException', (error) => {
    console.error('未捕获异常:', error);
});

process.on('unhandledRejection', (reason) => {
    console.error('未处理的Promise拒绝:', reason);
});

// 异步处理ComfyUI请求
async function processComfyUIRequest(workflow, taskId) {
    console.log(`开始异步处理任务: ${taskId}`);

    // 初始化任务状态
    taskStatus.set(taskId, {
        status: 'queued',
        progress: 10,
        position: 1,
        estimatedTime: '60-120秒',
        createdAt: new Date()
    });

    try {
        // 更新状态为处理中
        const task = taskStatus.get(taskId);
        task.status = 'processing';
        task.progress = 30;
        task.estimatedTime = '30-90秒';

        console.log(`发送请求到ComfyUI: ${taskId}`);

        // 发送到ComfyUI，增加超时时间
        const response = await axios.post(
            'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/prompt',
            {
                prompt: workflow  // 将工作流包装在prompt字段中
            },
            {
                timeout: 180000, // 3分钟超时
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            }
        );

        console.log(`ComfyUI响应成功: ${taskId}`, response.status);
        console.log('响应数据:', response.data);

        const promptId = response.data.prompt_id;
        console.log(`获得ComfyUI prompt_id: ${promptId}`);

        // 更新任务状态为处理中
        task.status = 'processing';
        task.progress = 60;
        task.estimatedTime = '30-60秒';
        task.promptId = promptId;

        // 等待ComfyUI处理完成并获取图片
        const imageResult = await waitForComfyUICompletion(promptId, taskId);

        // 更新任务状态为完成
        task.status = 'completed';
        task.progress = 100;
        task.result = {
            imageUrl: imageResult.imageUrl,
            prompt: workflow['27']?.inputs?.text || '生成完成',
            negativePrompt: workflow['20']?.inputs?.text || '',
            size: `${workflow['25']?.inputs?.width || 512}x${workflow['25']?.inputs?.height || 512}`,
            filename: imageResult.filename,
            promptId: promptId,
            generatedAt: new Date()
        };

        console.log(`任务完成: ${taskId}`, task.result);

    } catch (error) {
        console.error(`任务失败: ${taskId}`, error.message);

        const task = taskStatus.get(taskId);
        task.status = 'failed';
        task.progress = 0;

        if (error.code === 'ECONNABORTED') {
            task.error = 'ComfyUI处理超时，请稍后重试';
        } else if (error.response?.status === 524) {
            task.error = 'ComfyUI服务繁忙，请稍后重试';
        } else if (error.response?.status) {
            task.error = `ComfyUI服务错误 (${error.response.status})`;
        } else {
            task.error = `处理失败: ${error.message}`;
        }

        console.error(`任务错误详情: ${taskId}`, {
            status: error.response?.status,
            data: error.response?.data,
            code: error.code
        });
    }
}

// 等待ComfyUI处理完成并获取图片
async function waitForComfyUICompletion(promptId, taskId) {
    console.log(`开始等待ComfyUI完成: ${promptId}`);

    const maxWaitTime = 180000; // 3分钟最大等待时间
    const checkInterval = 5000; // 5秒检查一次
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
        try {
            // 检查任务是否被取消
            const task = taskStatus.get(taskId);
            if (task && task.status === 'cancelled') {
                throw new Error('任务已取消');
            }

            // 更新进度
            const elapsed = Date.now() - startTime;
            const progressPercent = Math.min(95, 60 + (elapsed / maxWaitTime) * 35);
            if (task) {
                task.progress = Math.round(progressPercent);
                console.log(`任务进度更新: ${taskId} - ${task.progress}%`);
            }

            // 先检查队列状态
            const queueResponse = await axios.get(
                'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/queue',
                { timeout: 10000 }
            );

            const queueData = queueResponse.data;
            const isInQueue = queueData.queue_running?.some(item => item[1] === promptId) ||
                             queueData.queue_pending?.some(item => item[1] === promptId);

            if (!isInQueue) {
                console.log(`任务不在队列中，检查历史记录: ${promptId}`);

                // 检查ComfyUI历史记录
                const historyResponse = await axios.get(
                    `https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/history/${promptId}`,
                    { timeout: 10000 }
                );

                const historyData = historyResponse.data;
                console.log(`历史记录检查: ${promptId}`, Object.keys(historyData));

                if (historyData[promptId]) {
                    const outputs = historyData[promptId].outputs;
                    console.log('找到输出:', outputs);

                    // 查找保存的图片（通常在节点17）
                    for (const nodeId in outputs) {
                        const nodeOutput = outputs[nodeId];
                        if (nodeOutput.images && nodeOutput.images.length > 0) {
                            const imageInfo = nodeOutput.images[0];
                            console.log('🖼️ 找到图片:', imageInfo);

                            // 构建图片URL
                            const imageUrl = `https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/view?filename=${imageInfo.filename}&subfolder=${imageInfo.subfolder || ''}&type=${imageInfo.type || 'output'}`;

                            console.log('🎉 图片URL:', imageUrl);

                            // 验证图片URL是否可访问
                            try {
                                const imageResponse = await axios.head(imageUrl, { timeout: 10000 });
                                console.log('✅ 图片URL验证成功，状态码:', imageResponse.status);
                            } catch (imageError) {
                                console.log('⚠️ 图片URL验证失败:', imageError.message);
                            }

                            return {
                                imageUrl: imageUrl,
                                filename: imageInfo.filename
                            };
                        }
                    }

                    console.log('⚠️ 历史记录中未找到图片输出');
                }
            } else {
                console.log(`任务还在队列中处理: ${promptId}`);
            }

            await new Promise(resolve => setTimeout(resolve, checkInterval));

        } catch (error) {
            console.error(`检查ComfyUI状态失败: ${promptId}`, error.message);

            // 如果是404错误，说明任务还在处理中
            if (error.response?.status === 404) {
                console.log('任务还在处理中，继续等待...');
                await new Promise(resolve => setTimeout(resolve, checkInterval));
                continue;
            }

            // 其他错误，继续等待一段时间
            await new Promise(resolve => setTimeout(resolve, checkInterval));
        }
    }

    // 超时返回错误信息
    console.log(`⏰ ComfyUI处理超时: ${promptId}`);
    throw new Error('图片生成超时，请稍后重试');
}
