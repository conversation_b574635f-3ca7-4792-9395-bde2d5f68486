const axios = require('axios');
const fs = require('fs').promises;

async function testComfyUIAPI() {
    try {
        console.log('开始测试ComfyUI接口...');
        
        // 读取工作流文件
        const workflowData = await fs.readFile('./reba.json', 'utf8');
        const workflow = JSON.parse(workflowData);
        
        // 修改提示词进行测试
        if (workflow['27'] && workflow['27'].inputs) {
            workflow['27'].inputs.text = 'a beautiful landscape, mountains, sunset, high quality, detailed';
        }
        
        // 修改负面提示词
        if (workflow['1'] && workflow['1'].inputs) {
            workflow['1'].inputs.text = 'worst quality, low quality, blurry';
        }
        if (workflow['20'] && workflow['20'].inputs) {
            workflow['20'].inputs.text = 'worst quality, low quality, blurry';
        }
        
        // 生成随机种子
        if (workflow['26'] && workflow['26'].inputs) {
            workflow['26'].inputs.seed = Math.floor(Math.random() * 1000000000);
        }
        
        console.log('发送请求到ComfyUI...');
        console.log('URL:', 'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/prompt');
        
        const response = await axios.post(
            'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/prompt',
            workflow,
            {
                timeout: 60000,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            }
        );
        
        console.log('请求成功！');
        console.log('状态码:', response.status);
        console.log('响应数据:', JSON.stringify(response.data, null, 2));
        
        return response.data;
        
    } catch (error) {
        console.error('测试失败:');
        console.error('错误信息:', error.message);
        
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
        
        if (error.code) {
            console.error('错误代码:', error.code);
        }
        
        throw error;
    }
}

// 运行测试
if (require.main === module) {
    testComfyUIAPI()
        .then(result => {
            console.log('测试完成，结果:', result);
            process.exit(0);
        })
        .catch(error => {
            console.error('测试失败:', error.message);
            process.exit(1);
        });
}

module.exports = testComfyUIAPI;
