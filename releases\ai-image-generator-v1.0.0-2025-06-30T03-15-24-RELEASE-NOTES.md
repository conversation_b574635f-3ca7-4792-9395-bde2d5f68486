# ai-image-generator v1.0.0

## 发布信息
- 版本: 1.0.0
- 构建时间: 2025/6/30 11:15:25
- 包含内容: 完整的生产环境部署包

## 部署说明
1. 解压文件到服务器目录
2. 参考 DEPLOYMENT.md 进行部署配置
3. 运行启动脚本开始服务

## 系统要求
- Node.js >= 14.0.0
- npm >= 6.0.0
- PM2 (推荐)

## 快速启动
```bash
# Linux/Mac
chmod +x start.sh
./start.sh

# Windows
start.bat
```

## 目录结构
```
ai-image-generator-v1.0.0-2025-06-30T03-15-24/
├── backend/           # 后端应用和依赖
├── workflow/          # ComfyUI工作流配置
├── DEPLOYMENT.md      # 详细部署指南
├── ecosystem.config.js # PM2配置文件
├── .env.example       # 环境变量示例
├── start.sh          # Linux/Mac启动脚本
└── start.bat         # Windows启动脚本
```

## 支持
如有问题，请查看 DEPLOYMENT.md 或联系开发团队。
