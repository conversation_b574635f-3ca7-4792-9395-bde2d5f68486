import axios from 'axios'

// 创建专用的axios实例用于ComfyUI配置API
const createComfyUIApiInstance = () => {
  return axios.create({
    baseURL: '/api/comfyui',
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

const api = createComfyUIApiInstance()

// 请求拦截器
api.interceptors.request.use(
  config => {
    console.log('发送ComfyUI配置请求:', config.method?.toUpperCase(), config.url, config.data)
    return config
  },
  error => {
    console.error('ComfyUI配置请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('收到ComfyUI配置响应:', response.status, response.data)
    return response.data
  },
  error => {
    console.error('ComfyUI配置响应错误:', error.response?.status, error.response?.data || error.message)
    
    const errorMessage = error.response?.data?.error || 
                        error.response?.data?.message || 
                        error.message || 
                        'ComfyUI配置请求失败'
    
    return Promise.reject(new Error(errorMessage))
  }
)

/**
 * 获取ComfyUI配置
 * @returns {Promise<Object>} 配置信息
 */
export const getComfyUIConfig = async () => {
  return await api.get('/config')
}

/**
 * 更新ComfyUI配置
 * @param {Object} config - 配置对象
 * @param {string} config.baseURL - ComfyUI基础地址
 * @param {number} config.timeout - 超时时间(毫秒)
 * @returns {Promise<Object>} 更新结果
 */
export const updateComfyUIConfig = async (config) => {
  return await api.post('/config', config)
}

/**
 * 测试ComfyUI连接
 * @param {string} baseURL - 要测试的ComfyUI地址
 * @returns {Promise<Object>} 测试结果
 */
export const testComfyUIConnection = async (baseURL) => {
  return await api.post('/test', { baseURL })
}

/**
 * 自动检测ComfyUI服务器
 * @param {Array<string>} candidates - 候选地址列表(可选)
 * @returns {Promise<Object>} 检测结果
 */
export const autoDetectComfyUI = async (candidates = []) => {
  return await api.post('/auto-detect', { candidates })
}

/**
 * 验证ComfyUI地址格式
 * @param {string} url - 要验证的URL
 * @returns {Object} 验证结果
 */
export function validateComfyUIUrl(url) {
  if (!url || typeof url !== 'string') {
    return {
      valid: false,
      error: 'URL不能为空'
    }
  }
  
  try {
    const urlObj = new URL(url)
    
    // 检查协议
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return {
        valid: false,
        error: '协议必须是 http 或 https'
      }
    }
    
    // 检查主机名
    if (!urlObj.hostname) {
      return {
        valid: false,
        error: '无效的主机名'
      }
    }
    
    // 检查是否是CloudStudio格式
    const isCloudStudio = /^[a-f0-9]+-+\d+\.ap-[a-z]+\.cloudstudio\.club$/.test(urlObj.hostname)
    
    return {
      valid: true,
      url: url.replace(/\/$/, ''), // 移除末尾斜杠
      isCloudStudio,
      domainPrefix: isCloudStudio ? urlObj.hostname.split('--')[0] : null
    }
  } catch (error) {
    return {
      valid: false,
      error: 'URL格式无效'
    }
  }
}

/**
 * 从URL中提取域名前缀
 * @param {string} url - 完整URL
 * @returns {string|null} 域名前缀
 */
export function extractDomainPrefix(url) {
  try {
    const validation = validateComfyUIUrl(url)
    return validation.domainPrefix || null
  } catch (error) {
    return null
  }
}

/**
 * 根据新的域名前缀生成新URL
 * @param {string} originalUrl - 原始URL
 * @param {string} newPrefix - 新的域名前缀
 * @returns {string|null} 新的URL
 */
export function generateNewUrl(originalUrl, newPrefix) {
  try {
    const validation = validateComfyUIUrl(originalUrl)
    if (!validation.valid || !validation.isCloudStudio) {
      return null
    }
    
    const urlObj = new URL(originalUrl)
    const oldPrefix = validation.domainPrefix
    
    if (oldPrefix && newPrefix) {
      const newHostname = urlObj.hostname.replace(oldPrefix, newPrefix)
      return `${urlObj.protocol}//${newHostname}${urlObj.pathname}`
    }
    
    return null
  } catch (error) {
    return null
  }
}

/**
 * 获取常用的ComfyUI候选地址
 * @returns {Array<string>} 候选地址列表
 */
export function getComfyUICandidates() {
  return [
    'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club',
    'http://localhost:8188',
    'http://127.0.0.1:8188',
    'http://localhost:8080',
    'http://127.0.0.1:8080'
  ]
}

/**
 * 检查ComfyUI配置状态
 * @returns {Promise<Object>} 状态信息
 */
export const checkComfyUIStatus = async () => {
  try {
    const config = await getComfyUIConfig()
    if (!config.success || !config.config.baseURL) {
      return {
        configured: false,
        connected: false,
        message: '未配置ComfyUI服务器'
      }
    }
    
    const testResult = await testComfyUIConnection(config.config.baseURL)
    return {
      configured: true,
      connected: testResult.success && testResult.connected,
      baseURL: config.config.baseURL,
      message: testResult.message || '状态未知'
    }
  } catch (error) {
    return {
      configured: false,
      connected: false,
      message: `检查状态失败: ${error.message}`
    }
  }
}

export default api
