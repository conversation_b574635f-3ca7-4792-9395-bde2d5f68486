<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI图片生成器</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 主页面 -->
        <div id="main-page" class="page active">
            <header class="header">
                <h1 class="title">AI图片生成器</h1>
                <p class="subtitle">输入您的创意描述，AI将为您生成精美图片</p>
            </header>

            <div class="input-section">
                <div class="input-container">
                    <textarea 
                        id="prompt-input" 
                        placeholder="请输入您想要生成的图片描述，例如：一只可爱的小猫在花园里玩耍，阳光明媚，高清摄影风格..."
                        maxlength="1000"
                    ></textarea>
                    <div class="char-count">
                        <span id="char-count">0</span>/1000
                    </div>
                </div>
                
                <div class="input-container negative-input-container">
                    <label for="negative-input" class="negative-label">
                        <span class="label-icon">🚫</span>
                        反向提示词（可选）
                        <span class="label-hint">描述不想要的内容</span>
                    </label>
                    <textarea
                        id="negative-input"
                        placeholder="例如：低质量、模糊、变形、多余的手指、错误的解剖结构..."
                        maxlength="500"
                    ></textarea>
                    <div class="char-count negative-char-count">
                        <span id="negative-char-count">0</span>/500
                    </div>
                </div>

                <div class="options-section">
                    <div class="option-group">
                        <label for="size-select">图片尺寸：</label>
                        <select id="size-select">
                            <option value="480x720">纵向 (480x720)</option>
                            <option value="720x480">横向 (720x480)</option>
                            <option value="720x720">正方形 (720x720)</option>
                            <option value="720x1080">高清纵向 (720x1080)</option>
                        </select>
                    </div>
                </div>

                <button id="generate-btn" class="generate-btn" disabled>
                    <span class="btn-text">生成图片</span>
                    <span class="btn-icon">✨</span>
                </button>
            </div>

            <div class="queue-info">
                <div class="queue-status">
                    <span class="queue-label">当前队列：</span>
                    <span id="queue-count" class="queue-count">0</span>
                    <span class="queue-text">个任务等待中</span>
                </div>
            </div>
        </div>

        <!-- 等待页面 -->
        <div id="waiting-page" class="page">
            <div class="waiting-container">
                <div class="loading-animation">
                    <div class="loading-spinner"></div>
                    <div class="loading-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
                
                <h2 class="waiting-title">正在生成您的图片...</h2>
                <p class="waiting-subtitle">AI正在努力创作中，请稍候</p>
                
                <div class="progress-info">
                    <div class="progress-bar">
                        <div id="progress-fill" class="progress-fill"></div>
                    </div>
                    <div class="progress-text">
                        <span id="progress-status">已加入队列</span>
                        <span id="queue-position">排队位置: <span id="position-number">1</span></span>
                    </div>
                </div>

                <div class="estimated-time">
                    <span>预计等待时间：</span>
                    <span id="estimated-time">30-60秒</span>
                </div>

                <button id="cancel-btn" class="cancel-btn">取消生成</button>
            </div>
        </div>

        <!-- 结果页面 -->
        <div id="result-page" class="page">
            <div class="result-container">
                <h2 class="result-title">生成完成！</h2>
                
                <div class="image-container">
                    <img id="generated-image" src="" alt="生成的图片" />
                    <div class="image-actions">
                        <button id="download-btn" class="action-btn primary">
                            <span>下载图片</span>
                            <span>📥</span>
                        </button>
                        <button id="share-btn" class="action-btn secondary">
                            <span>分享</span>
                            <span>🔗</span>
                        </button>
                    </div>
                </div>

                <div class="prompt-info">
                    <h3>使用的描述：</h3>
                    <p id="used-prompt"></p>
                </div>

                <div class="result-actions">
                    <button id="generate-again-btn" class="generate-btn">
                        <span class="btn-text">再次生成</span>
                        <span class="btn-icon">🔄</span>
                    </button>
                    <button id="new-prompt-btn" class="secondary-btn">
                        <span>新的创作</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 错误页面 -->
        <div id="error-page" class="page">
            <div class="error-container">
                <div class="error-icon">❌</div>
                <h2 class="error-title">生成失败</h2>
                <p id="error-message" class="error-message">抱歉，图片生成过程中出现了问题，请稍后重试。</p>
                
                <div class="error-actions">
                    <button id="retry-btn" class="generate-btn">
                        <span class="btn-text">重试</span>
                        <span class="btn-icon">🔄</span>
                    </button>
                    <button id="back-home-btn" class="secondary-btn">
                        <span>返回首页</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div id="toast" class="toast">
        <span id="toast-message"></span>
    </div>

    <script src="script.js"></script>
</body>
</html>
