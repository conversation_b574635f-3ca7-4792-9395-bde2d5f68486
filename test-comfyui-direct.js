const axios = require('axios');
const fs = require('fs').promises;

async function testComfyUIDirect() {
    console.log('开始直接测试ComfyUI接口...');
    
    try {
        // 读取工作流文件
        console.log('1. 读取工作流文件...');
        const workflowData = await fs.readFile('./reba.json', 'utf8');
        const workflow = JSON.parse(workflowData);
        console.log('✅ 工作流文件读取成功');
        
        // 修改提示词进行测试
        console.log('2. 修改工作流参数...');
        if (workflow['27'] && workflow['27'].inputs) {
            workflow['27'].inputs.text = 'a simple test image, beautiful landscape';
            console.log('✅ 正面提示词已更新');
        }
        
        // 修改负面提示词
        if (workflow['1'] && workflow['1'].inputs) {
            workflow['1'].inputs.text = 'worst quality, low quality';
        }
        if (workflow['20'] && workflow['20'].inputs) {
            workflow['20'].inputs.text = 'worst quality, low quality';
        }
        console.log('✅ 负面提示词已更新');
        
        // 生成随机种子
        if (workflow['26'] && workflow['26'].inputs) {
            workflow['26'].inputs.seed = Math.floor(Math.random() * 1000000000);
            console.log('✅ 随机种子已生成');
        }
        
        console.log('3. 发送请求到ComfyUI...');
        console.log('URL:', 'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/prompt');
        
        const startTime = Date.now();
        
        // 测试不同的超时设置
        const timeouts = [30000, 60000, 120000]; // 30秒, 1分钟, 2分钟
        
        for (const timeout of timeouts) {
            console.log(`\n尝试超时设置: ${timeout/1000}秒`);
            
            try {
                const response = await axios.post(
                    'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/prompt',
                    {
                        prompt: workflow  // 将工作流包装在prompt字段中
                    },
                    {
                        timeout: timeout,
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                            'User-Agent': 'ComfyUI-Test-Client/1.0'
                        }
                    }
                );
                
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                console.log('✅ 请求成功！');
                console.log('响应时间:', duration + 'ms');
                console.log('状态码:', response.status);
                console.log('响应头:', JSON.stringify(response.headers, null, 2));
                console.log('响应数据:', JSON.stringify(response.data, null, 2));
                
                return response.data;
                
            } catch (error) {
                console.log(`❌ 超时${timeout/1000}秒失败:`, error.message);
                
                if (error.response) {
                    console.log('响应状态:', error.response.status);
                    console.log('响应数据:', error.response.data);
                }
                
                if (error.code) {
                    console.log('错误代码:', error.code);
                }
                
                // 如果不是最后一个超时设置，继续尝试
                if (timeout !== timeouts[timeouts.length - 1]) {
                    console.log('尝试更长的超时时间...\n');
                    continue;
                }
            }
        }
        
        throw new Error('所有超时设置都失败了');
        
    } catch (error) {
        console.error('\n❌ 测试失败:');
        console.error('错误信息:', error.message);
        
        if (error.response) {
            console.error('HTTP状态:', error.response.status);
            console.error('响应头:', error.response.headers);
            console.error('响应数据:', error.response.data);
        }
        
        if (error.code) {
            console.error('错误代码:', error.code);
        }
        
        // 分析常见错误
        if (error.code === 'ECONNABORTED') {
            console.error('\n💡 分析: 请求超时');
            console.error('建议: ComfyUI处理时间较长，考虑使用异步方式');
        } else if (error.response?.status === 524) {
            console.error('\n💡 分析: Cloudflare超时 (524)');
            console.error('建议: ComfyUI服务器处理时间超过了Cloudflare的限制');
        } else if (error.response?.status === 502) {
            console.error('\n💡 分析: 网关错误 (502)');
            console.error('建议: ComfyUI服务可能暂时不可用');
        } else if (error.response?.status === 503) {
            console.error('\n💡 分析: 服务不可用 (503)');
            console.error('建议: ComfyUI服务器可能过载');
        }
        
        throw error;
    }
}

// 运行测试
if (require.main === module) {
    testComfyUIDirect()
        .then(result => {
            console.log('\n🎉 测试完成，结果:', result);
            process.exit(0);
        })
        .catch(error => {
            console.error('\n💥 测试失败');
            process.exit(1);
        });
}

module.exports = testComfyUIDirect;
