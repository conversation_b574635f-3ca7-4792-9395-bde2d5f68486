<template>
  <el-dialog
    v-model="visible"
    title="ComfyUI服务器配置"
    width="600px"
    :close-on-click-modal="false"
  >
    <div class="config-container">
      <el-alert
        title="配置ComfyUI服务器地址"
        description="当ComfyUI服务器重启后域名前缀会发生变化，请在此更新配置"
        type="info"
        :closable="false"
        style="margin-bottom: 20px"
      />
      
      <el-form :model="configForm" label-width="140px">
        <el-form-item label="ComfyUI地址">
          <el-input
            v-model="configForm.baseURL"
            placeholder="例如: https://xxx--8088.ap-shanghai.cloudstudio.club"
            clearable
          >
            <template #prepend>
              <el-icon><Link /></el-icon>
            </template>
          </el-input>
          <div class="form-hint">
            请输入完整的ComfyUI服务器地址，包含协议和端口
          </div>
        </el-form-item>
        
        <el-form-item label="超时时间(秒)">
          <el-input-number
            v-model="configForm.timeout"
            :min="5"
            :max="300"
            :step="5"
            style="width: 200px"
          />
          <div class="form-hint">
            请求超时时间，建议30-60秒
          </div>
        </el-form-item>
        
        <el-form-item label="连接测试">
          <el-button 
            type="primary" 
            :loading="testing" 
            @click="testConnection"
            :disabled="!configForm.baseURL"
          >
            <el-icon><Connection /></el-icon>
            测试连接
          </el-button>
          <el-button 
            type="success" 
            :loading="autoDetecting" 
            @click="autoDetect"
          >
            <el-icon><Search /></el-icon>
            自动检测
          </el-button>
          <span v-if="testResult" :class="testResultClass" style="margin-left: 10px;">
            {{ testResult }}
          </span>
        </el-form-item>
      </el-form>
      
      <div class="current-config">
        <h4>当前配置</h4>
        <p><strong>ComfyUI地址:</strong> {{ currentConfig.baseURL || '未配置' }}</p>
        <p><strong>超时时间:</strong> {{ currentConfig.timeout || 30 }}秒</p>
        <p><strong>域名前缀:</strong> 
          <el-tag size="small" type="info">
            {{ currentConfig.domainPrefix || '无' }}
          </el-tag>
        </p>
        <p><strong>状态:</strong> 
          <el-tag :type="isConnected ? 'success' : 'danger'" size="small">
            {{ isConnected ? '已连接' : '未连接' }}
          </el-tag>
        </p>
      </div>
      
      <div class="domain-prefix-helper">
        <h4>域名前缀快速更新</h4>
        <p class="helper-desc">如果只是域名前缀变化，可以快速更新：</p>
        <el-input
          v-model="newPrefix"
          placeholder="输入新的域名前缀"
          style="width: 300px; margin-right: 10px"
        >
          <template #prepend>前缀:</template>
        </el-input>
        <el-button 
          type="warning" 
          @click="updatePrefix"
          :disabled="!newPrefix"
        >
          快速更新
        </el-button>
        <div class="form-hint">
          例如：如果新地址是 https://abc123--8088.ap-shanghai.cloudstudio.club，则输入 abc123
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button @click="handleReset" type="warning">重置为默认</el-button>
        <el-button 
          type="primary" 
          @click="handleSave"
          :disabled="!configForm.baseURL || testing"
        >
          保存配置
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Link, Connection, Search } from '@element-plus/icons-vue'
import { 
  getComfyUIConfig, 
  updateComfyUIConfig, 
  testComfyUIConnection,
  autoDetectComfyUI
} from '../api/comfyuiApi.js'

export default {
  name: 'ComfyUIConfig',
  components: {
    Link,
    Connection,
    Search
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'config-changed'],
  setup(props, { emit }) {
    const visible = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value)
    })
    
    const configForm = reactive({
      baseURL: '',
      timeout: 30
    })
    
    const currentConfig = reactive({
      baseURL: '',
      timeout: 30,
      domainPrefix: '',
      connected: false
    })
    
    const testing = ref(false)
    const autoDetecting = ref(false)
    const testResult = ref('')
    const isConnected = ref(false)
    const newPrefix = ref('')
    
    const testResultClass = computed(() => {
      if (testResult.value.includes('成功')) {
        return 'test-success'
      } else if (testResult.value.includes('失败')) {
        return 'test-error'
      }
      return ''
    })
    
    // 监听对话框打开，加载当前配置
    watch(visible, (newVal) => {
      if (newVal) {
        loadCurrentConfig()
      }
    })
    
    const loadCurrentConfig = async () => {
      try {
        const response = await getComfyUIConfig()
        if (response.success) {
          const config = response.config
          
          // 更新表单
          configForm.baseURL = config.baseURL || ''
          configForm.timeout = (config.timeout || 30000) / 1000
          
          // 更新当前配置显示
          Object.assign(currentConfig, {
            baseURL: config.baseURL || '',
            timeout: (config.timeout || 30000) / 1000,
            domainPrefix: config.domainPrefix || ''
          })
          
          // 检查连接状态
          checkCurrentConnection()
        }
      } catch (error) {
        console.error('加载ComfyUI配置失败:', error)
        ElMessage.error('加载配置失败')
      }
    }
    
    const checkCurrentConnection = async () => {
      if (!currentConfig.baseURL) {
        isConnected.value = false
        return
      }
      
      try {
        const response = await testComfyUIConnection(currentConfig.baseURL)
        isConnected.value = response.success && response.connected
      } catch (error) {
        isConnected.value = false
      }
    }
    
    const testConnection = async () => {
      if (!configForm.baseURL) {
        ElMessage.warning('请先输入ComfyUI地址')
        return
      }
      
      testing.value = true
      testResult.value = ''
      
      try {
        const response = await testComfyUIConnection(configForm.baseURL)
        
        if (response.success && response.connected) {
          testResult.value = '✅ 连接成功'
          ElMessage.success('ComfyUI连接测试成功')
        } else {
          testResult.value = '❌ 连接失败'
          ElMessage.error('ComfyUI连接测试失败')
        }
      } catch (error) {
        testResult.value = `❌ 连接失败: ${error.message}`
        ElMessage.error(`连接测试失败: ${error.message}`)
      } finally {
        testing.value = false
      }
    }
    
    const autoDetect = async () => {
      autoDetecting.value = true
      testResult.value = ''
      
      try {
        ElMessage.info('正在自动检测ComfyUI服务器...')
        const response = await autoDetectComfyUI()
        
        if (response.success && response.detected) {
          configForm.baseURL = response.baseURL
          configForm.timeout = 30
          testResult.value = '✅ 自动检测成功'
          ElMessage.success(`检测到ComfyUI服务器: ${response.baseURL}`)
        } else {
          testResult.value = '❌ 未检测到可用服务器'
          ElMessage.warning('未检测到可用的ComfyUI服务器')
        }
      } catch (error) {
        testResult.value = `❌ 检测失败: ${error.message}`
        ElMessage.error(`自动检测失败: ${error.message}`)
      } finally {
        autoDetecting.value = false
      }
    }
    
    const updatePrefix = () => {
      if (!newPrefix.value || !currentConfig.baseURL) {
        ElMessage.warning('请输入新的域名前缀')
        return
      }
      
      try {
        // 替换域名前缀
        const oldPrefix = currentConfig.domainPrefix
        if (oldPrefix) {
          const newURL = currentConfig.baseURL.replace(oldPrefix, newPrefix.value)
          configForm.baseURL = newURL
          ElMessage.success('域名前缀已更新，请测试连接后保存')
        } else {
          ElMessage.warning('当前配置中未找到域名前缀')
        }
      } catch (error) {
        ElMessage.error('更新域名前缀失败')
      }
    }
    
    const handleSave = async () => {
      if (!configForm.baseURL) {
        ElMessage.warning('请输入ComfyUI地址')
        return
      }
      
      try {
        const response = await updateComfyUIConfig({
          baseURL: configForm.baseURL,
          timeout: configForm.timeout * 1000
        })
        
        if (response.success) {
          ElMessage.success('ComfyUI配置已保存')
          emit('config-changed')
          visible.value = false
          loadCurrentConfig()
        } else {
          ElMessage.error(response.error || '保存配置失败')
        }
      } catch (error) {
        ElMessage.error(`保存配置失败: ${error.message}`)
      }
    }
    
    const handleCancel = () => {
      visible.value = false
    }
    
    const handleReset = () => {
      configForm.baseURL = ''
      configForm.timeout = 30
      newPrefix.value = ''
      ElMessage.info('表单已重置')
    }
    
    // 组件挂载时加载配置
    onMounted(() => {
      loadCurrentConfig()
    })
    
    return {
      visible,
      configForm,
      currentConfig,
      testing,
      autoDetecting,
      testResult,
      testResultClass,
      isConnected,
      newPrefix,
      testConnection,
      autoDetect,
      updatePrefix,
      handleSave,
      handleCancel,
      handleReset
    }
  }
}
</script>

<style scoped>
.config-container {
  padding: 10px 0;
}

.form-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.current-config {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
}

.current-config h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.current-config p {
  margin: 5px 0;
  color: #606266;
}

.domain-prefix-helper {
  background: #fdf6ec;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #f5dab1;
  margin-top: 20px;
}

.domain-prefix-helper h4 {
  margin: 0 0 10px 0;
  color: #e6a23c;
}

.helper-desc {
  margin: 5px 0 10px 0;
  color: #606266;
  font-size: 13px;
}

.test-success {
  color: #67c23a;
  font-weight: 500;
}

.test-error {
  color: #f56c6c;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
