{"name": "ai-image-generator", "version": "1.0.0", "description": "AI图片生成器 - Vue前端 + Node.js后端", "scripts": {"install:frontend": "cd frontend && npm install", "install:backend": "cd backend && npm install", "install:all": "npm run install:frontend && npm run install:backend", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "dev": "echo \"请分别启动前后端服务：\" && echo \"前端: npm run dev:frontend\" && echo \"后端: npm run dev:backend\"", "build:frontend": "cd frontend && npm run build", "build": "node scripts/build.js", "start:backend": "cd backend && npm start", "start": "npm run build:frontend && npm run start:backend", "docker:build": "docker build -t ai-image-generator .", "docker:run": "docker-compose up -d"}, "keywords": ["ai", "image-generation", "vue", "nodejs", "comfyui", "workflow"], "author": "AI Assistant", "license": "MIT", "engines": {"node": ">=14.0.0"}}