const axios = require('axios');

async function quickTest() {
    console.log('🚀 快速测试新功能...\n');
    
    try {
        // 测试简化服务器
        const baseURL = 'http://localhost:3004';
        
        console.log('1️⃣ 测试健康检查...');
        const healthResponse = await axios.get(`${baseURL}/health`);
        console.log('✅ 健康检查成功:', healthResponse.data);
        
        console.log('\n2️⃣ 测试新的生成API...');
        const generateResponse = await axios.post(`${baseURL}/api/generate`, {
            prompt: 'beautiful mountain landscape at sunset',
            negativePrompt: 'blurry, low quality, distorted',
            size: '720x480'
        });
        console.log('✅ 生成请求成功:', generateResponse.data);
        
        const taskId = generateResponse.data.taskId;
        console.log(`📋 任务ID: ${taskId}`);
        
        console.log('\n3️⃣ 监控任务状态...');
        for (let i = 0; i < 10; i++) {
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            const statusResponse = await axios.get(`${baseURL}/api/status/${taskId}`);
            const status = statusResponse.data;
            
            console.log(`📊 检查 ${i+1}: 状态=${status.status}, 进度=${status.progress}%`);
            
            if (status.status === 'completed') {
                console.log('\n🎉 任务完成!');
                console.log('📸 结果:', JSON.stringify(status.result, null, 2));
                break;
            }
        }
        
        console.log('\n✨ 快速测试完成!');
        
    } catch (error) {
        console.error('\n❌ 测试失败:', error.message);
        if (error.response) {
            console.error('响应数据:', error.response.data);
        }
    }
}

quickTest();
