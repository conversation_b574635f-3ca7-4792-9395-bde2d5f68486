<template>
  <el-dialog
    v-model="visible"
    title="API路径测试"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="tester-container">
      <el-alert
        title="API路径测试工具"
        description="测试各个API端点的可访问性，帮助诊断API接口不存在的问题"
        type="info"
        :closable="false"
        style="margin-bottom: 20px"
      />
      
      <!-- 当前配置 -->
      <el-card shadow="never" style="margin-bottom: 20px">
        <template #header>
          <span>当前API配置</span>
        </template>
        <el-descriptions :column="2" size="small">
          <el-descriptions-item label="基础地址">
            {{ currentConfig.baseURL || '相对路径' }}
          </el-descriptions-item>
          <el-descriptions-item label="完整API地址">
            {{ getFullApiUrl() }}
          </el-descriptions-item>
          <el-descriptions-item label="超时时间">
            {{ currentConfig.timeout || 30000 }}ms
          </el-descriptions-item>
          <el-descriptions-item label="环境">
            {{ isDevelopment ? '开发环境' : '生产环境' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 测试按钮 -->
      <div class="test-actions">
        <el-button 
          type="primary" 
          :loading="testing" 
          @click="testAllEndpoints"
          :icon="VideoPlay"
        >
          测试所有端点
        </el-button>
        <el-button 
          type="success" 
          @click="testSingleEndpoint"
          :disabled="!customEndpoint"
          :icon="Connection"
        >
          测试自定义端点
        </el-button>
        <el-input
          v-model="customEndpoint"
          placeholder="输入自定义端点，如: /health"
          style="width: 200px; margin-left: 10px"
        />
      </div>
      
      <!-- 测试结果 -->
      <div v-if="testResults.length > 0" class="test-results">
        <el-card shadow="never">
          <template #header>
            <span>测试结果</span>
            <el-button 
              type="text" 
              @click="clearResults"
              style="float: right; padding: 3px 0"
            >
              清除
            </el-button>
          </template>
          
          <div class="results-summary">
            <el-statistic
              title="成功"
              :value="successCount"
              suffix="个"
              class="success-stat"
            />
            <el-statistic
              title="失败"
              :value="failureCount"
              suffix="个"
              class="failure-stat"
            />
            <el-statistic
              title="总计"
              :value="testResults.length"
              suffix="个"
              class="total-stat"
            />
          </div>
          
          <div class="results-list">
            <div 
              v-for="(result, index) in testResults" 
              :key="index"
              class="result-item"
              :class="result.success ? 'success' : 'failure'"
            >
              <div class="result-header">
                <el-icon :class="result.success ? 'success-icon' : 'error-icon'">
                  <Check v-if="result.success" />
                  <Close v-else />
                </el-icon>
                <span class="endpoint-name">{{ result.method }} {{ result.endpoint }}</span>
                <el-tag 
                  :type="result.success ? 'success' : 'danger'" 
                  size="small"
                >
                  {{ result.status }}
                </el-tag>
                <span class="response-time">{{ result.responseTime }}ms</span>
              </div>
              <div class="result-details">
                <p><strong>完整URL:</strong> {{ result.fullUrl }}</p>
                <p><strong>响应:</strong> {{ result.message }}</p>
                <p v-if="result.error"><strong>错误:</strong> {{ result.error }}</p>
              </div>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 建议 -->
      <div v-if="testResults.length > 0" class="suggestions">
        <el-card shadow="never">
          <template #header>
            <span>问题诊断建议</span>
          </template>
          
          <div v-if="allTestsPassed" class="all-success">
            <el-alert
              title="所有API端点测试通过"
              description="API配置正确，所有端点都可以正常访问"
              type="success"
              :closable="false"
            />
          </div>
          
          <div v-else class="has-failures">
            <el-alert
              title="发现API访问问题"
              type="warning"
              :closable="false"
            >
              <ul>
                <li v-if="hasNetworkErrors">网络连接问题：检查网络连接和服务器状态</li>
                <li v-if="has404Errors">404错误：检查API地址配置是否正确</li>
                <li v-if="has500Errors">服务器错误：检查后端服务器运行状态</li>
                <li v-if="hasCorsErrors">CORS错误：检查跨域配置</li>
              </ul>
            </el-alert>
            
            <div class="fix-suggestions">
              <h4>解决建议：</h4>
              <ol>
                <li>确认后端服务器正在运行</li>
                <li>检查API基础地址配置是否正确</li>
                <li>验证网络连接状态</li>
                <li>查看浏览器控制台的详细错误信息</li>
              </ol>
            </div>
          </div>
        </el-card>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { VideoPlay, Connection, Check, Close } from '@element-plus/icons-vue'
import { getApiConfig } from '../utils/apiConfig.js'

export default {
  name: 'ApiPathTester',
  components: {
    VideoPlay,
    Connection,
    Check,
    Close
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const visible = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value)
    })
    
    const testing = ref(false)
    const customEndpoint = ref('')
    const testResults = ref([])
    const currentConfig = reactive({})
    
    const isDevelopment = import.meta.env.DEV
    
    // 预定义的API端点
    const apiEndpoints = [
      { method: 'GET', endpoint: '/health', description: '健康检查' },
      { method: 'GET', endpoint: '/workflows', description: '获取工作流列表' },
      { method: 'GET', endpoint: '/queue-info', description: '获取队列信息' },
      { method: 'GET', endpoint: '/comfyui/config', description: 'ComfyUI配置' },
      { method: 'POST', endpoint: '/generate', description: '生成图片', data: { prompt: 'test' } }
    ]
    
    const successCount = computed(() => 
      testResults.value.filter(r => r.success).length
    )
    
    const failureCount = computed(() => 
      testResults.value.filter(r => !r.success).length
    )
    
    const allTestsPassed = computed(() => 
      testResults.value.length > 0 && failureCount.value === 0
    )
    
    const hasNetworkErrors = computed(() =>
      testResults.value.some(r => r.error && r.error.includes('Network'))
    )
    
    const has404Errors = computed(() =>
      testResults.value.some(r => r.status === 404)
    )
    
    const has500Errors = computed(() =>
      testResults.value.some(r => r.status >= 500)
    )
    
    const hasCorsErrors = computed(() =>
      testResults.value.some(r => r.error && r.error.includes('CORS'))
    )
    
    const getFullApiUrl = () => {
      const config = getApiConfig()
      if (config.baseURL) {
        return config.baseURL.replace(/\/$/, '') + '/api'
      }
      return window.location.origin + '/api'
    }
    
    const updateCurrentConfig = () => {
      const config = getApiConfig()
      Object.assign(currentConfig, config)
    }
    
    const testEndpoint = async (method, endpoint, data = null) => {
      const startTime = Date.now()
      const fullUrl = getFullApiUrl() + endpoint
      
      try {
        const options = {
          method,
          headers: {
            'Content-Type': 'application/json'
          }
        }
        
        if (data && (method === 'POST' || method === 'PUT')) {
          options.body = JSON.stringify(data)
        }
        
        const response = await fetch(fullUrl, options)
        const responseTime = Date.now() - startTime
        
        let responseData = ''
        try {
          responseData = await response.text()
        } catch (e) {
          responseData = 'Unable to read response'
        }
        
        return {
          success: response.ok,
          method,
          endpoint,
          fullUrl,
          status: response.status,
          message: response.ok ? 'Success' : `HTTP ${response.status}`,
          responseTime,
          error: response.ok ? null : responseData
        }
      } catch (error) {
        const responseTime = Date.now() - startTime
        return {
          success: false,
          method,
          endpoint,
          fullUrl,
          status: 0,
          message: 'Request failed',
          responseTime,
          error: error.message
        }
      }
    }
    
    const testAllEndpoints = async () => {
      testing.value = true
      testResults.value = []
      
      try {
        ElMessage.info('开始测试所有API端点...')
        
        for (const { method, endpoint, data } of apiEndpoints) {
          const result = await testEndpoint(method, endpoint, data)
          testResults.value.push(result)
        }
        
        const successCount = testResults.value.filter(r => r.success).length
        const totalCount = testResults.value.length
        
        if (successCount === totalCount) {
          ElMessage.success(`所有 ${totalCount} 个端点测试通过`)
        } else {
          ElMessage.warning(`${totalCount} 个端点中有 ${totalCount - successCount} 个失败`)
        }
      } catch (error) {
        ElMessage.error(`测试失败: ${error.message}`)
      } finally {
        testing.value = false
      }
    }
    
    const testSingleEndpoint = async () => {
      if (!customEndpoint.value) return
      
      testing.value = true
      
      try {
        const result = await testEndpoint('GET', customEndpoint.value)
        testResults.value.unshift(result)
        
        if (result.success) {
          ElMessage.success('端点测试成功')
        } else {
          ElMessage.error('端点测试失败')
        }
      } catch (error) {
        ElMessage.error(`测试失败: ${error.message}`)
      } finally {
        testing.value = false
      }
    }
    
    const clearResults = () => {
      testResults.value = []
      ElMessage.info('测试结果已清除')
    }
    
    const handleClose = () => {
      visible.value = false
    }
    
    watch(visible, (newVal) => {
      if (newVal) {
        updateCurrentConfig()
      }
    })
    
    return {
      visible,
      testing,
      customEndpoint,
      testResults,
      currentConfig,
      isDevelopment,
      successCount,
      failureCount,
      allTestsPassed,
      hasNetworkErrors,
      has404Errors,
      has500Errors,
      hasCorsErrors,
      getFullApiUrl,
      testAllEndpoints,
      testSingleEndpoint,
      clearResults,
      handleClose
    }
  }
}
</script>

<style scoped>
.tester-container {
  padding: 10px 0;
}

.test-actions {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 10px;
}

.results-summary {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.success-stat :deep(.el-statistic__number) {
  color: #67c23a;
}

.failure-stat :deep(.el-statistic__number) {
  color: #f56c6c;
}

.total-stat :deep(.el-statistic__number) {
  color: #409eff;
}

.results-list {
  max-height: 400px;
  overflow-y: auto;
}

.result-item {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 10px;
}

.result-item.success {
  border-color: #67c23a;
  background: #f0f9ff;
}

.result-item.failure {
  border-color: #f56c6c;
  background: #fef0f0;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.endpoint-name {
  font-weight: 500;
  flex: 1;
  font-family: 'Courier New', monospace;
}

.response-time {
  font-size: 12px;
  color: #909399;
}

.result-details {
  font-size: 12px;
  color: #606266;
}

.result-details p {
  margin: 2px 0;
  word-break: break-all;
}

.success-icon {
  color: #67c23a;
}

.error-icon {
  color: #f56c6c;
}

.suggestions {
  margin-top: 20px;
}

.fix-suggestions {
  margin-top: 15px;
}

.fix-suggestions h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.fix-suggestions ol {
  margin: 0;
  padding-left: 20px;
}

.fix-suggestions li {
  margin: 5px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
