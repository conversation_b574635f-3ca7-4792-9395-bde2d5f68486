const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3003;

console.log('启动模拟服务器...');

// 任务状态存储
const taskStatus = new Map();

// 基础中间件
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// 简单路由
app.get('/', (req, res) => {
    console.log('收到根路径请求');
    res.send('Mock Server is running!');
});

app.get('/health', (req, res) => {
    console.log('收到健康检查请求');
    res.json({ status: 'ok', timestamp: new Date() });
});

// 生成图片API（模拟版）
app.post('/api/generate', async (req, res) => {
    console.log('收到生成图片请求:', req.body);
    
    try {
        const { prompt, style, size } = req.body;
        
        if (!prompt || prompt.trim().length < 10) {
            return res.status(400).json({ error: '描述至少需要10个字符' });
        }
        
        console.log('开始处理生成请求...');
        
        // 生成任务ID
        const taskId = `mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        // 初始化任务状态
        taskStatus.set(taskId, {
            status: 'queued',
            progress: 10,
            position: 1,
            estimatedTime: '10-20秒',
            prompt: prompt,
            style: style,
            size: size,
            createdAt: new Date()
        });
        
        console.log(`任务创建成功: ${taskId}`);
        
        // 模拟异步处理
        simulateProcessing(taskId);
        
        // 立即返回任务ID
        res.json({
            taskId: taskId,
            status: 'queued',
            position: 1,
            estimatedTime: '10-20秒',
            message: '任务已提交，正在模拟处理中...'
        });
        
    } catch (error) {
        console.error('生成失败:', error.message);
        res.status(500).json({
            error: `生成失败: ${error.message}`
        });
    }
});

// 状态查询
app.get('/api/status/:taskId', (req, res) => {
    const taskId = req.params.taskId;
    console.log('收到状态查询:', taskId);
    
    const task = taskStatus.get(taskId);
    
    if (!task) {
        return res.status(404).json({ error: '任务不存在' });
    }
    
    res.json({
        status: task.status,
        progress: task.progress || 0,
        position: task.position || 0,
        estimatedTime: task.estimatedTime || '处理中',
        result: task.result,
        error: task.error
    });
});

// 队列信息
app.get('/api/queue-info', (req, res) => {
    console.log('收到队列信息请求');
    
    const queuedTasks = Array.from(taskStatus.values()).filter(task => 
        task.status === 'queued' || task.status === 'processing'
    );
    
    res.json({ 
        queueLength: queuedTasks.length,
        processingCount: queuedTasks.filter(task => task.status === 'processing').length
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`模拟服务器运行在 http://localhost:${PORT}`);
});

// 错误处理
process.on('uncaughtException', (error) => {
    console.error('未捕获异常:', error);
});

process.on('unhandledRejection', (reason) => {
    console.error('未处理的Promise拒绝:', reason);
});

// 模拟处理过程
function simulateProcessing(taskId) {
    console.log(`开始模拟处理任务: ${taskId}`);
    
    const task = taskStatus.get(taskId);
    if (!task) return;
    
    // 第一阶段：处理中
    setTimeout(() => {
        if (task.status === 'queued') {
            task.status = 'processing';
            task.progress = 30;
            task.estimatedTime = '5-10秒';
            console.log(`任务进入处理阶段: ${taskId}`);
        }
    }, 2000);
    
    // 第二阶段：进度更新
    setTimeout(() => {
        if (task.status === 'processing') {
            task.progress = 60;
            task.estimatedTime = '3-5秒';
            console.log(`任务进度更新: ${taskId} - 60%`);
        }
    }, 5000);
    
    // 第三阶段：即将完成
    setTimeout(() => {
        if (task.status === 'processing') {
            task.progress = 90;
            task.estimatedTime = '即将完成';
            console.log(`任务即将完成: ${taskId} - 90%`);
        }
    }, 8000);
    
    // 最终阶段：完成
    setTimeout(() => {
        if (task.status === 'processing') {
            task.status = 'completed';
            task.progress = 100;
            task.result = {
                imageUrl: `https://via.placeholder.com/${task.size || '512x512'}/667eea/ffffff?text=Mock+Generated`,
                prompt: task.prompt,
                style: task.style,
                size: task.size,
                generatedAt: new Date(),
                isMock: true
            };
            console.log(`任务完成: ${taskId}`);
        }
    }, 12000);
}
