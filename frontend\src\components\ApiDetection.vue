<template>
  <el-dialog
    v-model="visible"
    title="API服务器检测"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
  >
    <div class="detection-container">
      <el-alert
        title="正在检测API服务器"
        description="系统正在自动检测可用的API服务器地址，请稍候..."
        type="info"
        :closable="false"
        style="margin-bottom: 20px"
      />
      
      <div class="detection-progress">
        <el-progress
          :percentage="progress"
          :stroke-width="8"
          status="success"
          :show-text="false"
        />
        <div class="progress-text">{{ progressText }}</div>
      </div>
      
      <div class="detection-list">
        <h4>检测结果:</h4>
        <div 
          v-for="(result, index) in detectionResults" 
          :key="index"
          class="detection-item"
        >
          <el-icon 
            :class="result.status === 'success' ? 'success-icon' : 
                   result.status === 'failed' ? 'failed-icon' : 'loading-icon'"
          >
            <Check v-if="result.status === 'success'" />
            <Close v-else-if="result.status === 'failed'" />
            <Loading v-else />
          </el-icon>
          <span class="url">{{ result.url }}</span>
          <span :class="'status-' + result.status">{{ result.message }}</span>
        </div>
      </div>
      
      <div v-if="detectionComplete" class="detection-result">
        <el-alert
          v-if="foundUrl"
          :title="`检测完成！找到可用的API服务器: ${foundUrl}`"
          type="success"
          :closable="false"
        />
        <el-alert
          v-else
          title="未找到可用的API服务器"
          description="请手动配置API服务器地址"
          type="warning"
          :closable="false"
        />
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button 
          v-if="!detectionComplete"
          @click="handleSkip"
          type="info"
        >
          跳过检测
        </el-button>
        <el-button 
          v-if="detectionComplete && !foundUrl"
          @click="handleManualConfig"
          type="primary"
        >
          手动配置
        </el-button>
        <el-button 
          v-if="detectionComplete && foundUrl"
          @click="handleUseDetected"
          type="success"
        >
          使用检测到的地址
        </el-button>
        <el-button 
          v-if="detectionComplete"
          @click="handleClose"
        >
          关闭
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Close, Loading } from '@element-plus/icons-vue'
import { autoDetectApiUrl, setApiConfig } from '../utils/apiConfig.js'

export default {
  name: 'ApiDetection',
  components: {
    Check,
    Close,
    Loading
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'detection-complete', 'manual-config'],
  setup(props, { emit }) {
    const visible = ref(props.modelValue)
    const progress = ref(0)
    const progressText = ref('准备检测...')
    const detectionResults = reactive([])
    const detectionComplete = ref(false)
    const foundUrl = ref('')
    
    watch(() => props.modelValue, (newVal) => {
      visible.value = newVal
      if (newVal) {
        startDetection()
      }
    })
    
    watch(visible, (newVal) => {
      emit('update:modelValue', newVal)
    })
    
    const startDetection = async () => {
      progress.value = 0
      progressText.value = '开始检测API服务器...'
      detectionResults.length = 0
      detectionComplete.value = false
      foundUrl.value = ''
      
      const candidates = [
        'http://localhost:4001',
        'http://localhost:5000',
        'http://localhost:3000',
        'http://127.0.0.1:4001',
        'http://127.0.0.1:5000',
        'http://127.0.0.1:3000'
      ]
      
      let currentIndex = 0
      
      for (const url of candidates) {
        // 添加检测项
        detectionResults.push({
          url,
          status: 'testing',
          message: '检测中...'
        })
        
        progressText.value = `检测 ${url}...`
        progress.value = Math.round((currentIndex / candidates.length) * 100)
        
        try {
          // 模拟检测延迟
          await new Promise(resolve => setTimeout(resolve, 500))
          
          // 实际检测
          const response = await fetch(`${url}/api/health`, {
            method: 'GET',
            timeout: 5000
          })
          
          if (response.ok) {
            detectionResults[currentIndex].status = 'success'
            detectionResults[currentIndex].message = '连接成功'
            
            if (!foundUrl.value) {
              foundUrl.value = url
            }
          } else {
            detectionResults[currentIndex].status = 'failed'
            detectionResults[currentIndex].message = '连接失败'
          }
        } catch (error) {
          detectionResults[currentIndex].status = 'failed'
          detectionResults[currentIndex].message = '连接失败'
        }
        
        currentIndex++
      }
      
      progress.value = 100
      progressText.value = '检测完成'
      detectionComplete.value = true
      
      if (foundUrl.value) {
        ElMessage.success(`找到可用的API服务器: ${foundUrl.value}`)
      } else {
        ElMessage.warning('未找到可用的API服务器，请手动配置')
      }
    }
    
    const handleUseDetected = () => {
      if (foundUrl.value) {
        setApiConfig({ baseURL: foundUrl.value })
        ElMessage.success('API配置已保存')
        emit('detection-complete', foundUrl.value)
        visible.value = false
      }
    }
    
    const handleManualConfig = () => {
      emit('manual-config')
      visible.value = false
    }
    
    const handleSkip = () => {
      ElMessage.info('已跳过API检测')
      visible.value = false
    }
    
    const handleClose = () => {
      visible.value = false
    }
    
    return {
      visible,
      progress,
      progressText,
      detectionResults,
      detectionComplete,
      foundUrl,
      handleUseDetected,
      handleManualConfig,
      handleSkip,
      handleClose
    }
  }
}
</script>

<style scoped>
.detection-container {
  padding: 10px 0;
}

.detection-progress {
  margin-bottom: 30px;
}

.progress-text {
  text-align: center;
  margin-top: 10px;
  color: #606266;
  font-size: 14px;
}

.detection-list h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.detection-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detection-item:last-child {
  border-bottom: none;
}

.success-icon {
  color: #67c23a;
}

.failed-icon {
  color: #f56c6c;
}

.loading-icon {
  color: #409eff;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.url {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: #303133;
  min-width: 200px;
}

.status-success {
  color: #67c23a;
  font-weight: 500;
}

.status-failed {
  color: #f56c6c;
}

.status-testing {
  color: #409eff;
}

.detection-result {
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
