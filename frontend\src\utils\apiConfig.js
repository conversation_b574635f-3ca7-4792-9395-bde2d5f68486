import axios from 'axios'

// 默认配置
const DEFAULT_CONFIG = {
  baseURL: '', // 空字符串表示使用相对路径（开发环境通过Vite代理）
  timeout: 30000
}

// 配置存储键名
const CONFIG_STORAGE_KEY = 'ai_image_generator_api_config'

/**
 * 获取API配置
 * @returns {Object} API配置对象
 */
export function getApiConfig() {
  try {
    const stored = localStorage.getItem(CONFIG_STORAGE_KEY)
    if (stored) {
      const config = JSON.parse(stored)
      return { ...DEFAULT_CONFIG, ...config }
    }
  } catch (error) {
    console.warn('读取API配置失败:', error)
  }
  
  return { ...DEFAULT_CONFIG }
}

/**
 * 设置API配置
 * @param {Object} config - 配置对象
 * @param {string} config.baseURL - API基础地址
 * @param {number} config.timeout - 请求超时时间
 */
export function setApiConfig(config) {
  try {
    const currentConfig = getApiConfig()
    const newConfig = { ...currentConfig, ...config }
    
    localStorage.setItem(CONFIG_STORAGE_KEY, JSON.stringify(newConfig))
    
    // 触发配置变更事件
    window.dispatchEvent(new CustomEvent('api-config-changed', {
      detail: newConfig
    }))
    
    console.log('API配置已更新:', newConfig)
    return newConfig
  } catch (error) {
    console.error('保存API配置失败:', error)
    throw error
  }
}

/**
 * 重置API配置为默认值
 */
export function resetApiConfig() {
  try {
    localStorage.removeItem(CONFIG_STORAGE_KEY)
    window.dispatchEvent(new CustomEvent('api-config-changed', {
      detail: DEFAULT_CONFIG
    }))
    console.log('API配置已重置')
  } catch (error) {
    console.error('重置API配置失败:', error)
  }
}

/**
 * 获取完整的API地址
 * @param {string} path - API路径
 * @returns {string} 完整的API地址
 */
export function getApiUrl(path = '') {
  const config = getApiConfig()
  const baseURL = config.baseURL || ''
  
  // 如果没有配置baseURL，使用相对路径
  if (!baseURL) {
    return `/api${path.startsWith('/') ? path : '/' + path}`
  }
  
  // 确保baseURL不以斜杠结尾
  const cleanBaseURL = baseURL.replace(/\/$/, '')
  const cleanPath = path.startsWith('/') ? path : '/' + path
  
  return `${cleanBaseURL}/api${cleanPath}`
}

/**
 * 测试API连接
 * @param {string} customBaseURL - 自定义基础地址（可选）
 * @returns {Promise<boolean>} 连接是否成功
 */
export async function testApiConnection(customBaseURL = null) {
  try {
    const config = getApiConfig()
    const baseURL = customBaseURL || config.baseURL || ''
    
    let testUrl
    if (baseURL) {
      testUrl = `${baseURL.replace(/\/$/, '')}/api/health`
    } else {
      testUrl = '/api/health'
    }
    
    console.log('测试API连接:', testUrl)
    
    const response = await axios.get(testUrl, {
      timeout: 10000,
      headers: {
        'Accept': 'application/json'
      }
    })
    
    return response.status === 200 && response.data
  } catch (error) {
    console.error('API连接测试失败:', error.message)
    return false
  }
}

/**
 * 获取当前环境信息
 * @returns {Object} 环境信息
 */
export function getEnvironmentInfo() {
  const isDevelopment = import.meta.env.DEV
  const isProduction = import.meta.env.PROD
  const config = getApiConfig()
  
  return {
    isDevelopment,
    isProduction,
    currentBaseURL: config.baseURL,
    usingProxy: isDevelopment && !config.baseURL,
    mode: import.meta.env.MODE
  }
}

/**
 * 验证API地址格式
 * @param {string} url - 要验证的URL
 * @returns {Object} 验证结果
 */
export function validateApiUrl(url) {
  if (!url || typeof url !== 'string') {
    return {
      valid: false,
      error: 'URL不能为空'
    }
  }
  
  try {
    const urlObj = new URL(url)
    
    // 检查协议
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return {
        valid: false,
        error: '协议必须是 http 或 https'
      }
    }
    
    // 检查主机名
    if (!urlObj.hostname) {
      return {
        valid: false,
        error: '无效的主机名'
      }
    }
    
    return {
      valid: true,
      url: url.replace(/\/$/, '') // 移除末尾斜杠
    }
  } catch (error) {
    return {
      valid: false,
      error: 'URL格式无效'
    }
  }
}

/**
 * 自动检测可用的API地址
 * @param {Array<string>} candidates - 候选地址列表
 * @returns {Promise<string|null>} 可用的地址或null
 */
export async function autoDetectApiUrl(candidates = []) {
  const defaultCandidates = [
    'http://localhost:4001',
    'http://localhost:5000',
    'http://localhost:3000',
    'http://127.0.0.1:4001',
    'http://127.0.0.1:5000'
  ]
  
  const testUrls = [...candidates, ...defaultCandidates]
  
  console.log('自动检测API地址:', testUrls)
  
  for (const url of testUrls) {
    try {
      const isAvailable = await testApiConnection(url)
      if (isAvailable) {
        console.log('检测到可用的API地址:', url)
        return url
      }
    } catch (error) {
      // 继续尝试下一个
    }
  }
  
  console.log('未检测到可用的API地址')
  return null
}

// 导出默认配置供其他模块使用
export { DEFAULT_CONFIG }
