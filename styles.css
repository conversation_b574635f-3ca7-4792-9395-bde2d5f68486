/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 页面切换 */
.page {
    display: none;
    width: 100%;
    animation: fadeIn 0.5s ease-in-out;
}

.page.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 主页面样式 */
.header {
    text-align: center;
    margin-bottom: 40px;
}

.title {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.1rem;
    color: rgba(255,255,255,0.9);
    font-weight: 300;
}

.input-section {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.input-container {
    position: relative;
    margin-bottom: 25px;
}

#prompt-input, #negative-input {
    width: 100%;
    padding: 20px;
    border: 2px solid #e1e5e9;
    border-radius: 15px;
    font-size: 16px;
    font-family: inherit;
    resize: vertical;
    transition: all 0.3s ease;
    background: #fafbfc;
}

#prompt-input {
    min-height: 120px;
}

#negative-input {
    min-height: 80px;
    border-color: #f0ad4e;
    background: #fef9f0;
}

#prompt-input:focus, #negative-input:focus {
    outline: none;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

#prompt-input:focus {
    border-color: #667eea;
}

#negative-input:focus {
    border-color: #e67e22;
    box-shadow: 0 0 0 3px rgba(230, 126, 34, 0.1);
}

/* 反向提示词容器样式 */
.negative-input-container {
    border-left: 4px solid #f0ad4e;
    padding-left: 15px;
    background: linear-gradient(90deg, rgba(240, 173, 78, 0.05) 0%, transparent 100%);
    border-radius: 10px;
}

.negative-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: #e67e22;
    margin-bottom: 8px;
}

.label-icon {
    font-size: 16px;
}

.label-hint {
    font-size: 12px;
    color: #95a5a6;
    font-weight: 400;
    margin-left: auto;
}

.char-count {
    position: absolute;
    bottom: 10px;
    right: 15px;
    font-size: 12px;
    color: #666;
    background: rgba(255,255,255,0.9);
    padding: 2px 6px;
    border-radius: 4px;
}

.negative-char-count {
    background: rgba(240, 173, 78, 0.1);
    color: #e67e22;
    border: 1px solid rgba(240, 173, 78, 0.3);
}

.options-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 25px;
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.option-group label {
    font-weight: 500;
    color: #555;
    font-size: 14px;
}

.option-group select {
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 14px;
    background: white;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.option-group select:focus {
    outline: none;
    border-color: #667eea;
}

.generate-btn {
    width: 100%;
    padding: 18px 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 15px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    position: relative;
    overflow: hidden;
}

.generate-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.generate-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.generate-btn:active {
    transform: translateY(0);
}

.queue-info {
    background: rgba(255,255,255,0.95);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    backdrop-filter: blur(10px);
}

.queue-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 16px;
}

.queue-count {
    background: #667eea;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-weight: 600;
    min-width: 30px;
}

/* 等待页面样式 */
.waiting-container {
    background: white;
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.loading-animation {
    margin-bottom: 30px;
    position: relative;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-dots {
    display: flex;
    justify-content: center;
    gap: 8px;
}

.loading-dots span {
    width: 8px;
    height: 8px;
    background: #667eea;
    border-radius: 50%;
    animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

.waiting-title {
    font-size: 1.8rem;
    color: #333;
    margin-bottom: 10px;
    font-weight: 600;
}

.waiting-subtitle {
    color: #666;
    margin-bottom: 30px;
    font-size: 1.1rem;
}

.progress-info {
    margin-bottom: 25px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #666;
}

.estimated-time {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 25px;
    font-size: 14px;
    color: #555;
}

.cancel-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 10px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.cancel-btn:hover {
    background: #c82333;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .title {
        font-size: 2rem;
    }
    
    .input-section {
        padding: 20px;
    }
    
    .options-section {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .waiting-container {
        padding: 30px 20px;
    }
    
    .progress-text {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }
}

/* 结果页面样式 */
.result-container {
    background: white;
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.result-title {
    font-size: 1.8rem;
    color: #333;
    margin-bottom: 30px;
    font-weight: 600;
}

.image-container {
    margin-bottom: 30px;
    position: relative;
}

#generated-image {
    max-width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 20px;
}

.image-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn.primary {
    background: #28a745;
    color: white;
}

.action-btn.primary:hover {
    background: #218838;
    transform: translateY(-2px);
}

.action-btn.secondary {
    background: #6c757d;
    color: white;
}

.action-btn.secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.prompt-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 30px;
    text-align: left;
}

.prompt-info h3 {
    font-size: 1.1rem;
    color: #333;
    margin-bottom: 10px;
    font-weight: 600;
}

.prompt-info p {
    color: #666;
    line-height: 1.6;
    font-size: 14px;
}

.result-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.secondary-btn {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
    padding: 15px 25px;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.secondary-btn:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

/* 错误页面样式 */
.error-container {
    background: white;
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.error-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.error-title {
    font-size: 1.8rem;
    color: #dc3545;
    margin-bottom: 15px;
    font-weight: 600;
}

.error-message {
    color: #666;
    margin-bottom: 30px;
    line-height: 1.6;
}

.error-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Toast 通知样式 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #333;
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    font-size: 14px;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 1000;
    max-width: 300px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    background: #28a745;
}

.toast.error {
    background: #dc3545;
}

.toast.warning {
    background: #ffc107;
    color: #333;
}

@media (max-width: 480px) {
    .title {
        font-size: 1.8rem;
    }

    .subtitle {
        font-size: 1rem;
    }

    .generate-btn {
        font-size: 16px;
        padding: 15px 25px;
    }

    .result-container,
    .waiting-container,
    .error-container {
        padding: 30px 20px;
    }

    .image-actions,
    .result-actions,
    .error-actions {
        flex-direction: column;
    }

    .action-btn,
    .secondary-btn {
        width: 100%;
        justify-content: center;
    }

    .toast {
        right: 15px;
        left: 15px;
        max-width: none;
        transform: translateY(-100%);
    }

    .toast.show {
        transform: translateY(0);
    }
}
