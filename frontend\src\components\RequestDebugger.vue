<template>
  <el-dialog
    v-model="visible"
    title="请求调试工具"
    width="900px"
    :close-on-click-modal="false"
  >
    <div class="debugger-container">
      <el-alert
        title="请求调试工具"
        description="调试API请求参数和响应，帮助解决400错误等问题"
        type="info"
        :closable="false"
        style="margin-bottom: 20px"
      />
      
      <!-- 请求构建器 -->
      <el-card shadow="never" style="margin-bottom: 20px">
        <template #header>
          <span>构建生成请求</span>
        </template>
        
        <el-form :model="requestForm" label-width="120px">
          <el-form-item label="图片描述">
            <el-input
              v-model="requestForm.prompt"
              type="textarea"
              :rows="3"
              placeholder="输入图片描述..."
              show-word-limit
              maxlength="1000"
            />
            <div class="form-hint">
              当前长度: {{ requestForm.prompt.length }} 字符
            </div>
          </el-form-item>
          
          <el-form-item label="反向提示词">
            <el-input
              v-model="requestForm.negativePrompt"
              type="textarea"
              :rows="2"
              placeholder="输入不想要的元素..."
            />
          </el-form-item>
          
          <el-form-item label="图片尺寸">
            <el-select v-model="requestForm.size" placeholder="选择尺寸">
              <el-option label="720x480 (标准)" value="720x480" />
              <el-option label="1024x768 (高清)" value="1024x768" />
              <el-option label="512x512 (正方形)" value="512x512" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="工作流">
            <el-select v-model="requestForm.workflowId" placeholder="选择工作流">
              <el-option label="人像 (reba)" value="reba" />
              <el-option label="风景 (landscape)" value="landscape" />
              <el-option label="动漫 (anime)" value="anime" />
            </el-select>
          </el-form-item>
        </el-form>
        
        <div class="request-actions">
          <el-button 
            type="primary" 
            :loading="testing" 
            @click="testRequest"
            :icon="VideoPlay"
          >
            测试请求
          </el-button>
          <el-button 
            @click="clearForm"
            :icon="Delete"
          >
            清空表单
          </el-button>
          <el-button 
            @click="fillSampleData"
            :icon="DocumentAdd"
          >
            填充示例数据
          </el-button>
        </div>
      </el-card>
      
      <!-- 请求详情 -->
      <el-card shadow="never" style="margin-bottom: 20px">
        <template #header>
          <span>请求详情</span>
        </template>
        
        <div class="request-details">
          <h4>请求URL</h4>
          <el-input 
            :value="getRequestUrl()" 
            readonly 
            class="url-input"
          />
          
          <h4>请求体 (JSON)</h4>
          <el-input
            :value="getRequestBody()"
            type="textarea"
            :rows="8"
            readonly
            class="json-input"
          />
          
          <h4>参数验证</h4>
          <div class="validation-results">
            <div 
              v-for="(validation, index) in validationResults" 
              :key="index"
              class="validation-item"
              :class="validation.valid ? 'valid' : 'invalid'"
            >
              <el-icon :class="validation.valid ? 'success-icon' : 'error-icon'">
                <Check v-if="validation.valid" />
                <Close v-else />
              </el-icon>
              <span class="validation-name">{{ validation.name }}</span>
              <span class="validation-message">{{ validation.message }}</span>
            </div>
          </div>
        </div>
      </el-card>
      
      <!-- 测试结果 -->
      <div v-if="testResult" class="test-result">
        <el-card shadow="never">
          <template #header>
            <span>测试结果</span>
            <el-button 
              type="text" 
              @click="clearResult"
              style="float: right; padding: 3px 0"
            >
              清除
            </el-button>
          </template>
          
          <div class="result-summary">
            <el-tag 
              :type="testResult.success ? 'success' : 'danger'" 
              size="large"
            >
              {{ testResult.success ? '请求成功' : '请求失败' }}
            </el-tag>
            <span class="status-code">HTTP {{ testResult.status }}</span>
            <span class="response-time">{{ testResult.responseTime }}ms</span>
          </div>
          
          <div class="result-details">
            <h4>响应数据</h4>
            <el-input
              :value="JSON.stringify(testResult.data, null, 2)"
              type="textarea"
              :rows="10"
              readonly
              class="json-input"
            />
            
            <div v-if="testResult.error" class="error-details">
              <h4>错误信息</h4>
              <el-alert
                :title="testResult.error"
                type="error"
                :closable="false"
                show-icon
              />
            </div>
          </div>
        </el-card>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { VideoPlay, Delete, DocumentAdd, Check, Close } from '@element-plus/icons-vue'
import { getApiConfig } from '../utils/apiConfig.js'

export default {
  name: 'RequestDebugger',
  components: {
    VideoPlay,
    Delete,
    DocumentAdd,
    Check,
    Close
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const visible = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value)
    })
    
    const testing = ref(false)
    const testResult = ref(null)
    
    const requestForm = reactive({
      prompt: '',
      negativePrompt: '',
      size: '720x480',
      workflowId: 'reba'
    })
    
    const validationResults = computed(() => {
      const results = []
      
      // 验证prompt
      if (!requestForm.prompt) {
        results.push({
          name: 'prompt',
          valid: false,
          message: '图片描述不能为空'
        })
      } else if (requestForm.prompt.trim().length < 3) {
        results.push({
          name: 'prompt',
          valid: false,
          message: `图片描述太短 (${requestForm.prompt.trim().length}/3)`
        })
      } else if (requestForm.prompt.trim().length > 1000) {
        results.push({
          name: 'prompt',
          valid: false,
          message: `图片描述太长 (${requestForm.prompt.trim().length}/1000)`
        })
      } else {
        results.push({
          name: 'prompt',
          valid: true,
          message: `图片描述有效 (${requestForm.prompt.trim().length} 字符)`
        })
      }
      
      // 验证size
      if (requestForm.size) {
        results.push({
          name: 'size',
          valid: true,
          message: `图片尺寸: ${requestForm.size}`
        })
      } else {
        results.push({
          name: 'size',
          valid: false,
          message: '未选择图片尺寸'
        })
      }
      
      // 验证workflowId
      if (requestForm.workflowId) {
        results.push({
          name: 'workflowId',
          valid: true,
          message: `工作流: ${requestForm.workflowId}`
        })
      } else {
        results.push({
          name: 'workflowId',
          valid: false,
          message: '未选择工作流'
        })
      }
      
      return results
    })
    
    const getRequestUrl = () => {
      const config = getApiConfig()
      const baseURL = config.baseURL ? 
        config.baseURL.replace(/\/$/, '') + '/api' : 
        window.location.origin + '/api'
      return baseURL + '/generate'
    }
    
    const getRequestBody = () => {
      return JSON.stringify({
        prompt: requestForm.prompt,
        negativePrompt: requestForm.negativePrompt,
        size: requestForm.size,
        workflowId: requestForm.workflowId
      }, null, 2)
    }
    
    const testRequest = async () => {
      testing.value = true
      testResult.value = null
      
      try {
        const startTime = Date.now()
        const url = getRequestUrl()
        const body = {
          prompt: requestForm.prompt,
          negativePrompt: requestForm.negativePrompt,
          size: requestForm.size,
          workflowId: requestForm.workflowId
        }
        
        console.log('发送测试请求:', url, body)
        
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(body)
        })
        
        const responseTime = Date.now() - startTime
        let data
        
        try {
          data = await response.json()
        } catch (e) {
          data = await response.text()
        }
        
        testResult.value = {
          success: response.ok,
          status: response.status,
          responseTime,
          data,
          error: response.ok ? null : `HTTP ${response.status}: ${data.error || data.message || '未知错误'}`
        }
        
        if (response.ok) {
          ElMessage.success('请求测试成功')
        } else {
          ElMessage.error(`请求测试失败: HTTP ${response.status}`)
        }
        
      } catch (error) {
        const responseTime = Date.now() - startTime
        testResult.value = {
          success: false,
          status: 0,
          responseTime,
          data: null,
          error: error.message
        }
        ElMessage.error(`请求测试失败: ${error.message}`)
      } finally {
        testing.value = false
      }
    }
    
    const clearForm = () => {
      requestForm.prompt = ''
      requestForm.negativePrompt = ''
      requestForm.size = '720x480'
      requestForm.workflowId = 'reba'
      ElMessage.info('表单已清空')
    }
    
    const fillSampleData = () => {
      requestForm.prompt = 'A beautiful landscape with mountains and a lake, sunset lighting, photorealistic'
      requestForm.negativePrompt = 'blurry, low quality, distorted'
      requestForm.size = '720x480'
      requestForm.workflowId = 'reba'
      ElMessage.info('已填充示例数据')
    }
    
    const clearResult = () => {
      testResult.value = null
      ElMessage.info('测试结果已清除')
    }
    
    const handleClose = () => {
      visible.value = false
    }
    
    return {
      visible,
      testing,
      testResult,
      requestForm,
      validationResults,
      getRequestUrl,
      getRequestBody,
      testRequest,
      clearForm,
      fillSampleData,
      clearResult,
      handleClose
    }
  }
}
</script>

<style scoped>
.debugger-container {
  padding: 10px 0;
}

.form-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.request-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.request-details h4 {
  margin: 15px 0 8px 0;
  color: #303133;
  font-size: 14px;
}

.url-input, .json-input {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.validation-results {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.validation-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 6px;
}

.validation-item.valid {
  background: #f0f9ff;
  border: 1px solid #67c23a;
}

.validation-item.invalid {
  background: #fef0f0;
  border: 1px solid #f56c6c;
}

.validation-name {
  font-weight: 500;
  min-width: 80px;
}

.validation-message {
  color: #606266;
  font-size: 13px;
}

.success-icon {
  color: #67c23a;
}

.error-icon {
  color: #f56c6c;
}

.result-summary {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.status-code {
  font-weight: 500;
  color: #303133;
}

.response-time {
  font-size: 12px;
  color: #909399;
}

.result-details h4 {
  margin: 15px 0 8px 0;
  color: #303133;
  font-size: 14px;
}

.error-details {
  margin-top: 15px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
