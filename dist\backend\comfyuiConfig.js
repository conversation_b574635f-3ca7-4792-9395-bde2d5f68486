const fs = require('fs');
const path = require('path');

class ComfyUIConfig {
  constructor() {
    this.configFile = path.join(__dirname, 'comfyui-config.json');
    this.defaultConfig = {
      baseURL: 'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000
    };
    this.loadConfig();
  }

  /**
   * 加载配置文件
   */
  loadConfig() {
    try {
      if (fs.existsSync(this.configFile)) {
        const configData = fs.readFileSync(this.configFile, 'utf8');
        this.config = { ...this.defaultConfig, ...JSON.parse(configData) };
        console.log('✅ ComfyUI配置已加载:', this.config.baseURL);
      } else {
        this.config = { ...this.defaultConfig };
        this.saveConfig();
        console.log('📝 创建默认ComfyUI配置:', this.config.baseURL);
      }
    } catch (error) {
      console.error('❌ 加载ComfyUI配置失败:', error.message);
      this.config = { ...this.defaultConfig };
    }
  }

  /**
   * 保存配置文件
   */
  saveConfig() {
    try {
      fs.writeFileSync(this.configFile, JSON.stringify(this.config, null, 2));
      console.log('✅ ComfyUI配置已保存');
    } catch (error) {
      console.error('❌ 保存ComfyUI配置失败:', error.message);
    }
  }

  /**
   * 获取当前配置
   * @returns {Object} 配置对象
   */
  getConfig() {
    return { ...this.config };
  }

  /**
   * 更新配置
   * @param {Object} newConfig - 新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.saveConfig();
    console.log('🔄 ComfyUI配置已更新:', this.config.baseURL);
  }

  /**
   * 获取API URL
   * @param {string} endpoint - API端点
   * @returns {string} 完整的API URL
   */
  getApiUrl(endpoint) {
    const baseURL = this.config.baseURL.replace(/\/$/, '');
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : '/' + endpoint;
    return `${baseURL}${cleanEndpoint}`;
  }

  /**
   * 获取提示API URL
   * @returns {string} 提示API URL
   */
  getPromptUrl() {
    return this.getApiUrl('/api/prompt');
  }

  /**
   * 获取队列API URL
   * @returns {string} 队列API URL
   */
  getQueueUrl() {
    return this.getApiUrl('/api/queue');
  }

  /**
   * 获取历史API URL
   * @param {string} promptId - 提示ID
   * @returns {string} 历史API URL
   */
  getHistoryUrl(promptId) {
    return this.getApiUrl(`/api/history/${promptId}`);
  }

  /**
   * 获取图片查看URL
   * @param {string} filename - 文件名
   * @param {string} subfolder - 子文件夹
   * @param {string} type - 类型
   * @returns {string} 图片查看URL
   */
  getViewUrl(filename, subfolder = '', type = 'output') {
    const params = new URLSearchParams({
      filename,
      subfolder,
      type
    });
    return this.getApiUrl(`/api/view?${params.toString()}`);
  }

  /**
   * 验证配置
   * @param {string} baseURL - 要验证的基础URL
   * @returns {Promise<boolean>} 验证结果
   */
  async validateConfig(baseURL) {
    try {
      const axios = require('axios');
      const testUrl = `${baseURL.replace(/\/$/, '')}/api/queue`;
      
      console.log('🔍 验证ComfyUI配置:', testUrl);
      
      const response = await axios.get(testUrl, {
        timeout: 10000,
        headers: {
          'Accept': 'application/json'
        }
      });
      
      return response.status === 200;
    } catch (error) {
      console.error('❌ ComfyUI配置验证失败:', error.message);
      return false;
    }
  }

  /**
   * 自动检测可用的ComfyUI地址
   * @param {Array<string>} candidates - 候选地址列表
   * @returns {Promise<string|null>} 可用的地址或null
   */
  async autoDetect(candidates = []) {
    const defaultCandidates = [
      'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club',
      'http://localhost:8188',
      'http://127.0.0.1:8188'
    ];
    
    const testUrls = [...candidates, ...defaultCandidates];
    
    console.log('🔍 自动检测ComfyUI地址:', testUrls);
    
    for (const url of testUrls) {
      try {
        const isValid = await this.validateConfig(url);
        if (isValid) {
          console.log('✅ 检测到可用的ComfyUI地址:', url);
          return url;
        }
      } catch (error) {
        // 继续尝试下一个
      }
    }
    
    console.log('❌ 未检测到可用的ComfyUI地址');
    return null;
  }

  /**
   * 从URL中提取域名前缀
   * @param {string} url - 完整URL
   * @returns {string|null} 域名前缀
   */
  extractDomainPrefix(url) {
    try {
      const match = url.match(/https?:\/\/([a-f0-9]+)--/);
      return match ? match[1] : null;
    } catch (error) {
      return null;
    }
  }

  /**
   * 根据新的域名前缀更新URL
   * @param {string} newPrefix - 新的域名前缀
   * @returns {string} 更新后的URL
   */
  updateUrlWithPrefix(newPrefix) {
    const currentUrl = this.config.baseURL;
    const currentPrefix = this.extractDomainPrefix(currentUrl);
    
    if (currentPrefix && newPrefix) {
      const newUrl = currentUrl.replace(currentPrefix, newPrefix);
      this.updateConfig({ baseURL: newUrl });
      return newUrl;
    }
    
    return currentUrl;
  }

  /**
   * 获取配置状态
   * @returns {Object} 配置状态信息
   */
  getStatus() {
    return {
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      domainPrefix: this.extractDomainPrefix(this.config.baseURL),
      configFile: this.configFile,
      lastUpdated: fs.existsSync(this.configFile) ? 
        fs.statSync(this.configFile).mtime : null
    };
  }
}

module.exports = ComfyUIConfig;
