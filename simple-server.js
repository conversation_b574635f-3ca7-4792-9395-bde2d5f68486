const express = require('express');
const cors = require('cors');
const axios = require('axios');
const fs = require('fs').promises;

const app = express();
const PORT = 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

console.log('启动简化服务器...');

// 测试ComfyUI接口的路由
app.post('/api/test-comfyui', async (req, res) => {
    try {
        console.log('收到测试ComfyUI请求');
        
        // 读取工作流文件
        const workflowData = await fs.readFile('./reba.json', 'utf8');
        const workflow = JSON.parse(workflowData);
        
        // 修改提示词
        const userPrompt = req.body.prompt || 'a beautiful landscape';
        if (workflow['27'] && workflow['27'].inputs) {
            workflow['27'].inputs.text = userPrompt;
        }
        
        // 生成随机种子
        if (workflow['26'] && workflow['26'].inputs) {
            workflow['26'].inputs.seed = Math.floor(Math.random() * 1000000000);
        }
        
        console.log('发送请求到ComfyUI...');
        console.log('URL:', 'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/prompt');
        
        const response = await axios.post(
            'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/prompt',
            workflow,
            {
                timeout: 60000,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            }
        );
        
        console.log('ComfyUI响应成功:', response.status);
        console.log('响应数据:', response.data);
        
        res.json({
            success: true,
            status: response.status,
            data: response.data
        });
        
    } catch (error) {
        console.error('ComfyUI调用失败:', error.message);
        
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
        
        res.status(500).json({
            success: false,
            error: error.message,
            status: error.response?.status,
            data: error.response?.data
        });
    }
});

// 生成图片的简化API
app.post('/api/generate', async (req, res) => {
    try {
        console.log('收到生成图片请求:', req.body);
        
        const { prompt, style, size } = req.body;
        
        if (!prompt || prompt.trim().length < 10) {
            return res.status(400).json({ 
                error: '描述至少需要10个字符' 
            });
        }
        
        // 读取工作流文件
        const workflowData = await fs.readFile('./reba.json', 'utf8');
        const workflow = JSON.parse(workflowData);
        
        // 更新正面提示词 (节点27)
        if (workflow['27'] && workflow['27'].inputs) {
            workflow['27'].inputs.text = `best quality, masterpiece, ${prompt}`;
        }
        
        // 更新负面提示词 (节点1和20)
        const negativePrompt = 'worst quality, low quality, blurry, bad anatomy';
        if (workflow['1'] && workflow['1'].inputs) {
            workflow['1'].inputs.text = negativePrompt;
        }
        if (workflow['20'] && workflow['20'].inputs) {
            workflow['20'].inputs.text = negativePrompt;
        }
        
        // 更新图片尺寸
        if (size && workflow['25'] && workflow['25'].inputs) {
            const [width, height] = size.split('x').map(Number);
            workflow['25'].inputs.width = width;
            workflow['25'].inputs.height = height;
        }
        
        // 生成随机种子
        if (workflow['26'] && workflow['26'].inputs) {
            workflow['26'].inputs.seed = Math.floor(Math.random() * 1000000000);
        }
        
        console.log('发送工作流到ComfyUI...');
        
        const response = await axios.post(
            'https://aa50475786e041fabc7ac91533acf6ac--8088.ap-shanghai.cloudstudio.club/api/prompt',
            workflow,
            {
                timeout: 120000, // 2分钟超时
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            }
        );
        
        console.log('ComfyUI响应:', response.status, response.data);
        
        // 返回任务ID
        const taskId = response.data.prompt_id || response.data.id || `task_${Date.now()}`;
        
        res.json({
            taskId: taskId,
            status: 'processing',
            message: '图片生成中，请稍候...'
        });
        
    } catch (error) {
        console.error('生成失败:', error.message);
        
        res.status(500).json({
            error: `生成失败: ${error.message}`,
            details: error.response?.data
        });
    }
});

// 获取任务状态（模拟）
app.get('/api/status/:taskId', (req, res) => {
    const { taskId } = req.params;
    
    // 模拟返回完成状态
    res.json({
        status: 'completed',
        progress: 100,
        result: {
            imageUrl: 'https://via.placeholder.com/512x512/667eea/ffffff?text=AI+Generated',
            prompt: '生成完成',
            generatedAt: new Date()
        }
    });
});

// 队列信息
app.get('/api/queue-info', (req, res) => {
    res.json({
        queueLength: 0,
        processingCount: 0
    });
});

app.listen(PORT, () => {
    console.log(`简化服务器运行在 http://localhost:${PORT}`);
    console.log('可以访问 /api/test-comfyui 测试ComfyUI接口');
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n正在关闭服务器...');
    process.exit(0);
});
